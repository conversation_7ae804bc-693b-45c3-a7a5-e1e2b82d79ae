<?php

namespace App\Http\Controllers;

use App\Models\QuizQuestions;
use App\Models\QuizUserRespnose;
use App\Models\QuizResultSummary;
use Carbon\Carbon;
use App\Models\Book;
use App\Models\User;
use App\Models\Reader;
use App\Models\Review;
use App\Models\Wallet;
use App\Models\Activity;
use App\Models\Advertising;
use App\Models\BookQuiz;
use App\Models\QuizAnswers;
use App\Models\SubmittedLink;
use Illuminate\Http\Request;
use App\Models\SystemControl;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class ReaderController extends Controller
{
    public function read(Request $req, $amount, $slug) {
        $userId = Auth::user()->id;
        $weeklyLimit = SystemControl::where('key_type', 'NumberOfBooksTakenPerWeek')->value('value');
        $weeklyCount = Reader::where('userId', $userId)
            ->where('created_at', '>=', Carbon::now()->subDays(7))
            ->count();

        if ($weeklyCount >= $weeklyLimit) {
            return redirect()->route('vault')->withErrors(['error' => "Please come later. We are kindly allowed here to take only {$weeklyLimit} book(s) per week for a review"]);
        }

        $book = Book::where('slug', $slug)->first();
        $InitialPoints = SystemControl::where('key_type', 'InitialPoints')->first();
        $PercentageTodedduct = $InitialPoints->value / 100;

        if ($amount === 'paid') {
            $advertises = Advertising::where('bookId', $book->id)->where('bookPrice', '>', '0')->whereNot('bookPrice', 'KU')->first();
            $type = 'Paid';

            // Double-check that we're not getting a KU book
            if ($advertises && $advertises->bookPrice == 'KU') {
                $amount = 'kdp';
                $type = 'KDP';
            }
        } elseif ($amount == 'kdp') {
            $advertises = Advertising::where('bookId', $book->id)->where('bookPrice', 'KU')->first();
            $type = 'KDP';

            // If KU advertising not found, try to get any advertising for this book
            if (!$advertises) {
                $advertises = Advertising::where('bookId', $book->id)->first();

                // Set the correct type based on what we found
                if ($advertises) {
                    if ($advertises->bookPrice == 'KU') {
                        $type = 'KDP';
                    } elseif ($advertises->bookPrice > 0) {
                        $type = 'Paid';
                    } else {
                        $type = 'Free';
                    }
                }
            }
        } else {
            $advertises = Advertising::where('bookId', $book->id)->where('bookPrice', '<=', '0')->whereNot('bookPrice', 'KU')->first();
            $type = 'Free';

            // Double-check that we're not getting a KU book
            if ($advertises && $advertises->bookPrice == 'KU') {
                $amount = 'kdp';
                $type = 'KDP';
            }
        }

        // Check if advertising record exists
        if (!$advertises) {
            return redirect()->route('vault')->withErrors(['error' => 'Book advertising information not found. Please contact support.']);
        }

        // Calculate Total Points To Cut From User Initially.
        $systemPoints = SystemControl::where('key_type', 'commision')->value('value');
        $initialPoints = (int)$advertises->points - (int)$systemPoints;
        $tenPercentPoints = $initialPoints * $PercentageTodedduct;

        // Check if Book Exists.
        $isBookExists = Reader::where('bookId', $book->id)
            ->where('userId', Auth::user()->id)
            ->where('type', $amount)
            ->first();

        if ($isBookExists) {
            // If Book Exists Then Update the Book.
            $isBookExists->update([
                'endDate' => now()->addDays(7),
                'status' => $req->status ?? 1,
                'rpoints' => $tenPercentPoints,
            ]);
        } else {
            // Before creating a new assignment, check if weekly limit is reached
            $weeklyLimit = (int) SystemControl::where('key_type', 'NumberOfBooksTakenPerWeek')->value('value');
            $weeklyCount = Reader::where('userId', $userId)->where('created_at', '>=', Carbon::now()->subDays(7))->count();
            if ($weeklyCount >= $weeklyLimit) {
                return redirect()->route('vault')->withErrors(['error' => "Please come later. We are kindly allowed here to take only {$weeklyLimit} book(s) per week for a review"]);
            }

            // If Not Exists Create New One.
            $newreader = new Reader;
            $newreader->bookId = $book->id;
            $newreader->userId = Auth::user()->id;
            \Log::info('Before Carbon::now():', ['now' => now()]);
            $newreader->startDate = Carbon::now()->format('Y-m-d H:i:s');
            \Log::info('After Carbon::now() and format():', ['startDate' => $newreader->startDate]);
            $newreader->endDate = now()->addDays(7)->format('Y-m-d H:i:s');
            \Log::info('endDate after format():', ['endDate' => $newreader->endDate]);
            $newreader->status = $req->status ?? 1;
            $newreader->type = $type;
            $newreader->rpoints = $tenPercentPoints;
            $newreader->save();
        }

        $advertises->decrement('reeader_required', 1);
        $advertises->save();

        // Balance Math
        $wallet = Wallet::where('userId', '=', Auth::user()->id)->first();
        $curBal = $wallet->currentBalance;
        $turbal = $tenPercentPoints;
        $newBal = $curBal + $turbal;
        $wallet->currentBalance = $newBal;
        $wallet->save();

        // Notify Book Author.
        $activity = new Activity;
        $activity->userid = $book->publish_by;
        $activity->bookId = $book->id;
        $activity->reason = "Your book has been taken for review ($book->title).";
        $activity->notification_type = 'author';
        $activity->related_id = $book->id;
        $activity->save();

        // Notify Reviewer About The Book.
        if ($advertises->points != 0 || $advertises->points != 'KU') {
            $AdvertisingPoint = $tenPercentPoints;
        } else {
            $AdvertisingPoint = 'Free';
        }

        $userNotify = new Activity;
        $userNotify->userid = Auth()->user()->id;
        $userNotify->reason = "You received initial $AdvertisingPoint tokens for taking book for review.";
        $userNotify->notification_type = 'reader';
        $userNotify->related_id = $book->id;
        $userNotify->save();
        $isBookExists = Reader::where('bookId', $book->id)
            ->where('userId', $userId)
            ->where('type', $amount)
            ->first();
        if (!$isBookExists) {
            $weeklyLimit = (int) SystemControl::where('key_type', 'NumberOfBooksTakenPerWeek')->value('value');
            $weeklyCount = Reader::where('userId', $userId)
                ->where('created_at', '>=', Carbon::now()->subDays(7))
                ->count();
            if ($weeklyCount >= $weeklyLimit) {
                return redirect()->route('vault')->withErrors(['error' => "Please come later. We are kindly allowed here to take only {$weeklyLimit} book(s) per week for a review"]);
            }
        }

        return redirect(route('reader'))->withSuccess('Book Assigned To You');
    }

    public function returnReaderPage()
    {
        $userId = Auth::user()->id;
        $allReviews = Review::with('book')->where('userid', $userId)->orderBy('created_at', 'desc')->get();

        // First check if any assignments are overdue and should be auto-cancelled
        $this->checkAndCancelOverdueAssignments($userId);

        $readers = Reader::where('userId', $userId)->where('status', 1)->get();

        if ($readers->isEmpty()) {
            // Handle the case when there are no Reader records
            $books = null;
        } else {
            // Extract the bookIds from the Reader records
            $bookIds = $readers->where('status', 1)->pluck('bookId')->toArray();

            // Retrieve all the associated Book records
            $books = Book::whereIn('id', $bookIds)->get();

            $books = Reader::where('userId', $userId)->where('status', 1)->with('book')->get();
        }

        // return response()->json($books);

        return view('dashboard.reader', compact('books', 'allReviews'));
    }

    /**
     * Check and cancel any overdue assignments for a user
     */
    private function checkAndCancelOverdueAssignments($userId)
    {
        // Get active assignments for this user
        $activeAssignments = Reader::with(['book'])->where('userId', $userId)
            ->where('status', 1)
            ->get();

        // Get the configurable overdue hours setting
        $overdueCancelHours = SystemControl::where('key_type', 'overdue_cancel_hours')->value('value') ?? 48;

        foreach ($activeAssignments as $assignment) {
            // Get the turnaround time for this assignment
            $advertising = Advertising::where('bookId', $assignment->bookId)->first();
            if (!$advertising) continue;

            $turnaroundTime = $advertising->TurnAroundTime;

            // Calculate if assignment is overdue
            $startTime = Carbon::parse($assignment->created_at);
            $deadline = $startTime->copy()->addDays($turnaroundTime);
            $now = Carbon::now();

            // Calculate hours remaining
            $hoursLeft = $now->diffInHours($deadline, false);

            // If assignment is overdue by the configured hours, cancel it
            if ($hoursLeft <= -$overdueCancelHours) {
                $this->cancelOverdueAssignment($assignment);
            }
        }
    }

    public function reviewPage($id, $slug)
    {
        $userId = Auth::user()->id;

        $getActiveBooks = Reader::with(['book', 'book.categories', 'book.reviews'])->where('userId', $userId)->where('status', 1)->where('bookId', $id)->first();

        $systemPoints = SystemControl::where('key_type', 'commision')->value('value');

        if ($getActiveBooks) {
            // Ensure consistent book type handling across all pages
            $bookType = $getActiveBooks->type;

            if ($bookType == 'KDP') {
                $advertising = Advertising::where('bookId', $getActiveBooks->book->id)->where('bookPrice', 'KU')->first();
                if (!$advertising) {
                    $advertising = Advertising::where('bookId', $getActiveBooks->book->id)->first();
                }
            } elseif ($bookType == 'Paid') {
                $advertising = Advertising::where('bookId', $getActiveBooks->book->id)->where('bookPrice', '>', '0')->whereNot('bookPrice', 'KU')->first();
                if (!$advertising) {
                    $advertising = Advertising::where('bookId', $getActiveBooks->book->id)->first();
                }
            } else {
                $advertising = Advertising::where('bookId', $getActiveBooks->book->id)->where('bookPrice', '<=', '0')->whereNot('bookPrice', 'KU')->first();
                if (!$advertising) {
                    $advertising = Advertising::where('bookId', $getActiveBooks->book->id)->first();
                }
            }

            // Ensure advertising record matches the stored reader type
            if ($advertising) {
                $turnaroundTime = $advertising->TurnAroundTime;

                if ($bookType == 'Paid') {
                    $VPPoints = SystemControl::where('key_type', 'VP')->where('key', $advertising->bookPrice)->value('value');
                    $TotalPoints = $advertising->points;
                } elseif ($bookType == 'KDP') {
                    $VPPoints = SystemControl::where('key_type', 'KU')->value('value');
                    $TotalPoints = $advertising->points;
                } else {
                    $VPPoints = SystemControl::where('key_type', 'free')->value('value');
                    $TotalPoints = $advertising->points;
                }
            }

            // Calculate remaining time based on created_at timestamp and turnaround time
            $startTime = Carbon::parse($getActiveBooks->created_at);
            $deadline = $startTime->copy()->addDays($turnaroundTime);
            $now = Carbon::now();

            // Calculate hours remaining
            $hoursLeft = $now->diffInHours($deadline, false);

            // Get the configurable overdue hours setting
            $overdueCancelHours = SystemControl::where('key_type', 'overdue_cancel_hours')->value('value') ?? 48;

            // Format display time
            if ($hoursLeft > 24) {
                $daysLeft = ceil($hoursLeft / 24);
                $displayTime = $daysLeft . ' day' . ($daysLeft != 1 ? 's' : '') . ' left';
            } else {
                $displayTime = max(0, $hoursLeft) . ' hour' . ($hoursLeft != 1 ? 's' : '') . ' left';
            }

            // Check if assignment is overdue by the configured hours and should be auto-canceled
            if ($hoursLeft <= -$overdueCancelHours) {
                // Cancel the assignment
                $this->cancelOverdueAssignment($getActiveBooks);
                return redirect()->route('vault')->with('warning', 'Your assignment was canceled due to being overdue.');
            }

            // Send notification when assignment becomes overdue
            if ($hoursLeft <= 0 && !$getActiveBooks->overdue_notified) {
                $activity = new Activity;
                $activity->userid = $userId;
                $activity->bookId = $id;
                $activity->reason = "Your Assignment is overdue. Please complete it within {$overdueCancelHours} hours or it will be canceled.";
                $activity->notification_type = 'reader';
                $activity->related_id = $id;
                $activity->save();

                // Mark that we've sent the overdue notification
                $getActiveBooks->overdue_notified = true;
                $getActiveBooks->save();
            }

            $minimumMcqs = SystemControl::where('key_type', 'minmcqs')->value('value');
            $mintruefalse = SystemControl::where('key_type', 'mintruefalse')->value('value');
            $quizs = BookQuiz::where('book_id', $id)->with('question.answers')->get();
            $reviewExists = QuizUserRespnose::where('book_id', $id)->where('user_id', Auth::user()->id)->first();

            // Get all reviews by this user for the assignment history section
            $allReviews = Review::with('book')->where('userid', $userId)->orderBy('created_at', 'desc')->get();

            $ReviewTimeLimit = \App\Models\SystemControl::where('key_type', 'ReviewTimeLimit')->first();
            return view('dashboard.review', compact(
                'getActiveBooks',
                'advertising',
                'TotalPoints',
                'displayTime',
                'hoursLeft',
                'quizs',
                'reviewExists',
                'minimumMcqs',
                'mintruefalse',
                'ReviewTimeLimit',
                'systemPoints',
                'overdueCancelHours',
                'allReviews'
            ));
        } else {
            $getActiveBooks = null;
            // Get all reviews by this user for the assignment history section
            $allReviews = Review::with('book')->where('userid', $userId)->orderBy('created_at', 'desc')->get();
            return view('dashboard.review', compact('getActiveBooks', 'allReviews'));
        }
    }

    private function handleAssignmentCancellation($reader, $isAutoCancel = false)
    {
        // Get the exact initial points that were added
        $pointsToDeduct = $reader->rpoints;

        // Deduct points from wallet
        $wallet = Wallet::where('userId', $reader->userId)->first();
        if ($wallet) {
            $wallet->currentBalance -= $pointsToDeduct;
            $wallet->save();
        }

        // Return book to vault
        $advertising = Advertising::where('bookId', $reader->bookId)->first();
        if ($advertising) {
            $advertising->increment('reeader_required', 1);
        }

        // Reader-specific notification about point deduction
        $readerActivity = new Activity;
        $readerActivity->userid = $reader->userId;
        $readerActivity->bookId = $reader->bookId;
        $readerActivity->reason = $isAutoCancel
            ? "Assignment was auto-cancelled due to deadline expiration. {$pointsToDeduct} points have been deducted from your balance."
            : "Assignment cancelled. {$pointsToDeduct} points have been deducted from your balance.";
        $readerActivity->notification_type = 'reader';
        $readerActivity->related_id = $reader->bookId;
        $readerActivity->save();

        \Log::info('Reader notification created', [
            'notification_id' => $readerActivity->id,
            'reader_id' => $reader->userId,
            'book_id' => $reader->bookId,
            'notification_type' => $readerActivity->notification_type,
            'reason' => $readerActivity->reason
        ]);

        // Author notification only for auto-cancellation
        if ($isAutoCancel) {
            try {
                // Ensure book is loaded
                if (!$reader->relationLoaded('book')) {
                    $reader->load('book');
                }

                // If book relationship still not loaded, try to get it directly
                if (!$reader->book) {
                    $book = \App\Models\Book::find($reader->bookId);
                    if ($book) {
                        $reader->book = $book;
                    } else {
                        \Log::error('Could not find book with ID: ' . $reader->bookId);
                    }
                }

                // Only proceed if we have a valid book with author information
                if ($reader->book && $reader->book->publish_by) {
                    // Create author notification - this is for the book's activity section AND header dropdown
                    $authorActivity = new Activity;
                    $authorActivity->userid = $reader->book->publish_by; // Set the author's ID as userid (recipient)
                    $authorActivity->bookId = $reader->bookId;
                    $authorActivity->reason = "Unfortunately the Reader failed to complete your Assignment. Your book was returned to the Vault and we are looking for a new Reader!";
                    $authorActivity->notification_type = 'author';
                    $authorActivity->related_id = $reader->bookId;

                    // Save and log the notification ID
                    $authorActivity->save();
                    $notificationId = $authorActivity->id;

                    // Get the author's books for debugging
                    $authorBooks = Book::where('publish_by', $reader->book->publish_by)->pluck('id')->toArray();

                    \Log::info('Author notification created successfully', [
                        'notification_id' => $notificationId,
                        'book_id' => $reader->bookId,
                        'author_id' => $reader->book->publish_by,
                        'userid_field' => $authorActivity->userid,
                        'notification_type' => $authorActivity->notification_type,
                        'reason' => $authorActivity->reason,
                        'author_books' => $authorBooks,
                        'book_in_author_books' => in_array($reader->bookId, $authorBooks)
                    ]);

                    // Verify the notification can be retrieved with the header query
                    $canBeRetrieved = Activity::where('notification_type', 'author')
                        ->where(function($query) use ($reader, $authorBooks) {
                            $query->whereIn('bookId', $authorBooks)
                                  ->orWhere('userid', $reader->book->publish_by);
                        })
                        ->where('id', $notificationId)
                        ->exists();

                    \Log::info('Notification retrievable check', [
                        'notification_id' => $notificationId,
                        'can_be_retrieved' => $canBeRetrieved
                    ]);
                } else {
                    // If we can't get the book relationship, try to find the book and author directly
                    $book = \App\Models\Book::find($reader->bookId);
                    if ($book && $book->publish_by) {
                        $authorActivity = new Activity;
                        $authorActivity->userid = $book->publish_by;
                        $authorActivity->bookId = $reader->bookId;
                        $authorActivity->reason = "Unfortunately the Reader failed to complete your Assignment. Your book was returned to the Vault and we are looking for a new Reader!";
                        $authorActivity->notification_type = 'author';
                        $authorActivity->related_id = $reader->bookId;

                        // Save and log the notification ID
                        $authorActivity->save();
                        $notificationId = $authorActivity->id;

                        // Get the author's books for debugging
                        $authorBooks = Book::where('publish_by', $book->publish_by)->pluck('id')->toArray();

                        \Log::info('Author notification created via direct book lookup', [
                            'notification_id' => $notificationId,
                            'book_id' => $reader->bookId,
                            'author_id' => $book->publish_by,
                            'userid_field' => $authorActivity->userid,
                            'notification_type' => $authorActivity->notification_type,
                            'reason' => $authorActivity->reason,
                            'author_books' => $authorBooks,
                            'book_in_author_books' => in_array($reader->bookId, $authorBooks)
                        ]);

                        // Verify the notification can be retrieved with the header query
                        $canBeRetrieved = Activity::where('notification_type', 'author')
                            ->where(function($query) use ($book, $authorBooks) {
                                $query->whereIn('bookId', $authorBooks)
                                      ->orWhere('userid', $book->publish_by);
                            })
                            ->where('id', $notificationId)
                            ->exists();

                        \Log::info('Notification retrievable check (direct lookup)', [
                            'notification_id' => $notificationId,
                            'can_be_retrieved' => $canBeRetrieved
                        ]);
                    } else {
                        \Log::error('Failed to create author notification - could not determine book author', [
                            'reader_id' => $reader->id,
                            'book_id' => $reader->bookId
                        ]);
                    }
                }
            } catch (\Exception $e) {
                \Log::error('Error creating author notification: ' . $e->getMessage(), [
                    'reader_id' => $reader->id,
                    'book_id' => $reader->bookId
                ]);
            }
        }

        // Delete the reader record
        $reader->delete();

        return $pointsToDeduct;
    }

    public function cancelAssignment($id)
    {
        $reader = Reader::with('book')->where('userId', Auth::user()->id)
            ->where('bookId', $id)
            ->where('status', 1)
            ->first();

        if (!$reader) {
            return redirect()->back()->with('error', 'Assignment not found.');
        }

        $pointsDeducted = $this->handleAssignmentCancellation($reader, false);
        return redirect()->route('vault')->with('success', "Assignment cancelled. {$pointsDeducted} points have been deducted from your balance.");
    }

    private function cancelOverdueAssignment($reader)
    {
        try {
            // Ensure the book relationship is loaded for proper author notification
            if (!$reader->relationLoaded('book')) {
                $reader->load('book');
            }

            // Double-check that we have a valid book with author information
            if (!$reader->book) {
                \Log::error('Book relationship not loaded properly for reader ID: ' . $reader->id);
                // Try to load the book directly
                $book = Book::find($reader->bookId);
                if ($book) {
                    $reader->book = $book;
                } else {
                    \Log::error('Could not find book with ID: ' . $reader->bookId);
                }
            }

            $this->handleAssignmentCancellation($reader, true);
        } catch (\Exception $e) {
            \Log::error('Error in cancelOverdueAssignment: ' . $e->getMessage(), [
                'reader_id' => $reader->id ?? null,
                'book_id' => $reader->bookId ?? null
            ]);
            // Still try to cancel the assignment even if there was an error
            $this->handleAssignmentCancellation($reader, true);
        }
    }

    public function getSingleBook($slug)
    {
        $book = Book::where('slug', $slug)->first();
        $book1 = Book::where('slug', $slug)->first();
        $reader = Reader::where('bookId', $book->id)
            ->where('userId', Auth::user()->id)
            ->where('status', 1)
            ->first();
        $review = Review::where('bookId', $book->id)->where('userid', Auth::user()->id)->first();
        $advertising = Advertising::where('bookId', $book->id)->first();
        $allReviews = Review::where('bookId', $book->id)->get();

        // Calculate remaining days based on reader's startDate and endDate
        $now = Carbon::now();
        $endDate = Carbon::parse($reader->endDate);

        $remainingDays = max(0, $now->diffInDays($endDate));

        $displayTime = $remainingDays . ' day' . ($remainingDays != 1 ? 's' : '') . ' left';

        return view('reader.reader-single-book', compact('book', 'reader', 'book1', 'review', 'advertising', 'displayTime', 'allReviews'));
    }

    public function postQuizAnswer(Request $req)
    {
        \Log::info('Quiz submission started');

        // Check review time limit
        $bookId = $req->input('book_id');
        \Log::info('Book ID: ' . $bookId);

        // Validate required input
        if (!$bookId) {
            return response()->json(['error' => 'Invalid book ID'], 400);
        }

        $reader = Reader::where('bookId', $bookId)
            ->where('userId', Auth::user()->id)
            ->where('status', 1) // Only check active books
            ->first();

        \Log::info('Reader record:', ['reader' => $reader ? $reader->toArray() : null]);
        \Log::info('Raw reader startDate:', ['rawReaderStartDate' => $reader->startDate]); // Log raw reader startDate

        if (!$reader) {
            $errorMessage = "Could not find your active reading record for this book.";
            \Log::info('No reader record found');
            return response()->json(['error' => $errorMessage], 403);
        }

        // Get the minimum hours required between taking book and submitting review
        $ReviewTimeInBetween = SystemControl::where('key_type', 'ReviewTimeLimit')->value('value');
        \Log::info('Review time limit:', ['limit' => $ReviewTimeInBetween]);

        \Log::info('ReviewTimeInBetween value:', ['ReviewTimeInBetween' => $ReviewTimeInBetween]);

        if ($ReviewTimeInBetween != null && $ReviewTimeInBetween > 0) {
            $startTime = Carbon::parse($reader->created_at);
            $now = Carbon::now();

            // Calculate hours elapsed since taking the book
            $hoursElapsed = $startTime->diffInHours($now);

            \Log::info('Time check:', [
                'startTime' => $startTime->toDateTimeString(),
                'rawStartDate' => $reader->startDate, // Log raw startDate
                'startTimeParsed' => $startTime->toDateTimeString(), // Log parsed startDate
                'now' => $now->toDateTimeString(),
                'hoursElapsed' => $hoursElapsed,
                'minRequired' => $ReviewTimeInBetween
            ]);
            \Log::info('Time comparison:', ['hoursElapsed' => $hoursElapsed, 'ReviewTimeInBetween' => $ReviewTimeInBetween, 'limitNotMet' => $hoursElapsed < $ReviewTimeInBetween]);

            $remainingHours = $ReviewTimeInBetween - $hoursElapsed;
            if ($hoursElapsed < $ReviewTimeInBetween) {
                $errorMessage = "Please take your time to read the book. You need to wait {$remainingHours} more hour(s) before you can submit a review.";
                \Log::info('Time limit not met, returning error');
                return redirect()->back()->withErrors(['quizError' => $errorMessage])->withInput();
            }
        } else {
            \Log::info('Review time limit check skipped or limit is 0');
            $remainingHours = 0; // Set to 0 if no limit
        }

        // If we get here, the time limit check has passed
        \Log::info('Time limit check passed, proceeding with quiz submission');

        $input = $req->all();
        \Log::info('All form inputs:', $input);

        // Initialize total correct answers counter
        $totalCorrectAnswers = 0;

        // Log all form keys for debugging
        $formKeys = array_keys($input);
        \Log::info('All form keys:', $formKeys);

        // Process True/False questions
        $trueFalseCount = 0;
        $trueFalseCorrectCount = 0;
        foreach ($input as $key => $value) {
            if (strpos($key, 'trueFalse-') === 0) {
                $trueFalseCount++;
                $questionId = str_replace('trueFalse-', '', $key);
                $userAnswer = $req->input('trueFalse-' . $questionId);

                if (!$userAnswer) {
                    \Log::warning('Empty True/False answer for question ' . $questionId);
                    continue;
                }

                $qId = (int) $questionId;

                \Log::info('Processing True/False answer:', [
                    'key' => $key,
                    'question_id' => $qId,
                    'user_answer' => $userAnswer,
                    'user_answer_type' => gettype($userAnswer)
                ]);

                // Ensure userAnswer is treated as integer for database storage
                $userAnswerId = (int) $userAnswer;

                // Save the user response to database
                $answer = new QuizUserRespnose;
                $answer->book_id = $req->input('book_id');
                $answer->user_id = Auth::user()->id;
                $answer->question_id = $qId;
                $answer->Answer = $userAnswerId; // Store as integer
                $answer->save();

                // Get the answer record to check is_correct value directly
                $answerRecord = \DB::table('quiz_answers')->where('id', $userAnswerId)->first();

                if ($answerRecord) {
                    \Log::info('True/False answer record found:', [
                        'answer_id' => $userAnswerId,
                        'is_correct' => $answerRecord->is_correct,
                        'is_correct_type' => gettype($answerRecord->is_correct)
                    ]);

                    // Check if is_correct is truthy using multiple approaches
                    // This handles both integer 1, string "1", true, or other truthy values
                    $isCorrect = false;

                    // Try explicit numeric comparison
                    if ($answerRecord->is_correct == 1) {
                        $isCorrect = true;
                        \Log::info('True/False answer is correct (numeric comparison)');
                    }
                    // Try boolean casting
                    else if ((bool)$answerRecord->is_correct === true) {
                        $isCorrect = true;
                        \Log::info('True/False answer is correct (boolean casting)');
                    }
                    // Try string comparison for "1" or "true"
                    else if (is_string($answerRecord->is_correct) &&
                             (strtolower($answerRecord->is_correct) === "1" ||
                              strtolower($answerRecord->is_correct) === "true")) {
                        $isCorrect = true;
                        \Log::info('True/False answer is correct (string comparison)');
                    }
                } else {
                    \Log::warning('True/False answer record not found for ID: ' . $userAnswerId);
                    $isCorrect = false;
                }

                if ($isCorrect) {
                    $trueFalseCorrectCount++;
                    \Log::info('Correct True/False answer:', [
                        'question_id' => $qId,
                        'user_answer_id' => $userAnswerId,
                        'true_false_correct_count' => $trueFalseCorrectCount
                    ]);
                } else {
                    \Log::info('Incorrect True/False answer:', [
                        'question_id' => $qId,
                        'user_answer_id' => $userAnswerId
                    ]);
                }
            }
        }

        \Log::info('True/False questions summary:', [
            'total_true_false_questions' => $trueFalseCount,
            'correct_true_false_answers' => $trueFalseCorrectCount
        ]);

        // Process Multiple Choice questions
        $mcqCount = 0;
        $mcqCorrectCount = 0;
        foreach ($input as $key => $value) {
            if (strpos($key, 'multi_choice_question_') === 0) {
                try {
                    $mcqCount++;
                    $questionId = str_replace('multi_choice_question_', '', $key);
                    $userAnswer = $req->input('multi_choice_question_' . $questionId);

                    if (!$userAnswer) {
                        \Log::warning('Empty Multiple Choice answer for question ' . $questionId);
                        continue; // Skip if no answer provided
                    }

                    $qId = (int) $questionId;
                    // Ensure consistent integer handling
                    $userAnswerId = (int) $userAnswer;

                    \Log::info('Processing Multiple Choice answer:', [
                        'key' => $key,
                        'question_id' => $qId,
                        'user_answer_id' => $userAnswerId,
                        'user_answer_type' => gettype($userAnswerId)
                    ]);

                    // Save user's response
                    $answer = new QuizUserRespnose;
                    $answer->book_id = $req->input('book_id');
                    $answer->user_id = Auth::user()->id;
                    $answer->question_id = $qId;
                    $answer->Answer = $userAnswerId;
                    $answer->save();

                    // Get the answer record to check is_correct value directly
                    $answerRecord = \DB::table('quiz_answers')->where('id', $userAnswerId)->first();

                    if ($answerRecord) {
                        \Log::info('MCQ answer record found:', [
                            'answer_id' => $userAnswerId,
                            'is_correct' => $answerRecord->is_correct,
                            'is_correct_type' => gettype($answerRecord->is_correct)
                        ]);

                        // Check if is_correct is truthy using multiple approaches
                        // This handles both integer 1, string "1", true, or other truthy values
                        $isCorrect = false;

                        // Try explicit numeric comparison
                        if ($answerRecord->is_correct == 1) {
                            $isCorrect = true;
                            \Log::info('MCQ answer is correct (numeric comparison)');
                        }
                        // Try boolean casting
                        else if ((bool)$answerRecord->is_correct === true) {
                            $isCorrect = true;
                            \Log::info('MCQ answer is correct (boolean casting)');
                        }
                        // Try string comparison for "1" or "true"
                        else if (is_string($answerRecord->is_correct) &&
                                 (strtolower($answerRecord->is_correct) === "1" ||
                                  strtolower($answerRecord->is_correct) === "true")) {
                            $isCorrect = true;
                            \Log::info('MCQ answer is correct (string comparison)');
                        }
                    } else {
                        \Log::warning('MCQ answer record not found for ID: ' . $userAnswerId);
                        $isCorrect = false;
                    }

                    if ($isCorrect) {
                        $mcqCorrectCount++;
                        \Log::info('Correct Multiple Choice answer:', [
                            'question_id' => $qId,
                            'selected_answer_id' => $userAnswerId,
                            'mcq_correct_count' => $mcqCorrectCount
                        ]);
                    } else {
                        \Log::info('Incorrect Multiple Choice answer:', [
                            'question_id' => $qId,
                            'selected_answer_id' => $userAnswerId
                        ]);
                    }
                } catch (\Exception $e) {
                    \Log::error('Error processing quiz answer: ' . $e->getMessage(), [
                        'question_id' => $questionId ?? null,
                        'user_answer' => $userAnswer ?? null,
                        'user_id' => Auth::user()->id,
                        'trace' => $e->getTraceAsString()
                    ]);
                    continue; // Skip this answer and continue with others
                }
            }
        }

        \Log::info('Multiple choice questions summary:', [
            'total_mcq_questions' => $mcqCount,
            'correct_mcq_answers' => $mcqCorrectCount
        ]);

        // Calculate total correct answers - THIS IS THE KEY FIX
        $totalCorrectAnswers = $trueFalseCorrectCount + $mcqCorrectCount;

        // Log detailed information about the final counts
        \Log::info('Final quiz summary:', [
            'total_questions' => $trueFalseCount + $mcqCount,
            'true_false_correct' => $trueFalseCorrectCount,
            'mcq_correct' => $mcqCorrectCount,
            'total_correct_answers' => $totalCorrectAnswers
        ]);

        try {
            // Ensure we have integer values by explicit casting
            $trueFalseCorrectCount = (int)$trueFalseCorrectCount;
            $mcqCorrectCount = (int)$mcqCorrectCount;
            $totalCorrectAnswers = $trueFalseCorrectCount + $mcqCorrectCount;

            \Log::info('Final calculation with explicit typecasting:', [
                'true_false_correct' => $trueFalseCorrectCount,
                'true_false_type' => gettype($trueFalseCorrectCount),
                'mcq_correct' => $mcqCorrectCount,
                'mcq_type' => gettype($mcqCorrectCount),
                'total_correct' => $totalCorrectAnswers,
                'total_type' => gettype($totalCorrectAnswers)
            ]);

            // Final verification that the total is correct
            if ($totalCorrectAnswers !== ($trueFalseCorrectCount + $mcqCorrectCount)) {
                \Log::error('Total correct answer mismatch after calculation', [
                    'calculated_total' => $totalCorrectAnswers,
                    'sum_of_parts' => ($trueFalseCorrectCount + $mcqCorrectCount)
                ]);
                // Force the correct value
                $totalCorrectAnswers = (int)$trueFalseCorrectCount + (int)$mcqCorrectCount;
            }

            \Log::info('Processing quiz results:', [
                'total_correct_answers' => $totalCorrectAnswers
            ]);

            $percentageToDeduct = SystemControl::where('key_type', 'questionsPercentage')
                                            ->where('key', $totalCorrectAnswers)
                                            ->value('value') ?? 0;

            \Log::info('Calculated percentage:', [
                'true_false_correct' => $trueFalseCorrectCount,
                'mcq_correct' => $mcqCorrectCount,
                'total_correct_answers' => $totalCorrectAnswers,
                'percentage' => $percentageToDeduct
            ]);

            $InitialPointsPercentage = SystemControl::where('key_type', 'InitialPoints')
                                                ->value('value') ?? 0;
            $InitialPointsPercentage = $InitialPointsPercentage / 100;

            $TwentyPercentage = $req->points * $InitialPointsPercentage;
            $finalPoints = $req->points - $TwentyPercentage;
            $points = $finalPoints * $percentageToDeduct / 100;

            $wallet = Wallet::where('userId', Auth::user()->id)->first();
            if (!$wallet) {
                throw new \Exception('Wallet not found for user');
            }

            $newBalance = $wallet->currentBalance + $points;
            $wallet->currentBalance = $newBalance;
            $wallet->save();

            $activity = new Activity;
            $activity->userid = Auth::user()->id;
            $activity->reason = "You received additional $points tokens for completing a Quiz.";
            $activity->save();

            // Get the book slug for the redirect
            $book = Book::find($bookId);
            $slug = $book->slug;

            // Collect question details for storing in the summary
            $questionDetails = [];
            $quizResponses = QuizUserRespnose::where('book_id', $bookId)
                ->where('user_id', Auth::user()->id)
                ->with('question', 'question.answers')
                ->get();

            foreach ($quizResponses as $response) {
                $question = $response->question;
                $userAnswerId = $response->Answer;
                $correctAnswer = null;
                $userAnswerText = null;
                $isCorrect = false;

                // Find the user's selected answer and the correct answer
                foreach ($question->answers as $answer) {
                    if ($answer->id == $userAnswerId) {
                        $userAnswerText = $answer->answer;
                        $isCorrect = $answer->is_correct == 1;
                    }

                    if ($answer->is_correct == 1) {
                        $correctAnswer = $answer->answer;
                    }
                }

                $questionDetails[] = [
                    'question_id' => $question->id,
                    'question_text' => $question->question,
                    'question_type' => $question->questions_type,
                    'user_answer_id' => $userAnswerId,
                    'user_answer_text' => $userAnswerText,
                    'correct_answer' => $correctAnswer,
                    'is_correct' => $isCorrect
                ];
            }

            // Store the quiz result summary
            $quizSummary = new QuizResultSummary();
            $quizSummary->book_id = $bookId;
            $quizSummary->user_id = Auth::user()->id;
            $quizSummary->total_questions = $trueFalseCount + $mcqCount;
            $quizSummary->correct_answers = $totalCorrectAnswers;
            $quizSummary->points_awarded = $points;
            $quizSummary->question_details = $questionDetails;
            $quizSummary->save();

            // Create a detailed success message with HTML formatting
            $formattedPoints = \App\Helpers\NumberHelper::formatBalance($points);
            $successMessage = "You Got $totalCorrectAnswers Answers Right! and received $formattedPoints points.";

            // Add detailed breakdown of questions and answers
            $successMessage .= "<div class='quiz-results'>";
            $successMessage .= "<div class='summary'>Quiz Results:</div>";
            $successMessage .= "<div class='summary'>Total Questions: " . ($trueFalseCount + $mcqCount) . " | Correct Answers: $totalCorrectAnswers</div>";

            foreach ($questionDetails as $index => $detail) {
                $questionNumber = $index + 1;
                $resultClass = $detail['is_correct'] ? 'correct' : 'incorrect';
                $resultIcon = $detail['is_correct'] ? '✓' : '✗';

                $successMessage .= "<div class='question $resultClass'>";
                $successMessage .= "<div><span class='check-icon'>$resultIcon</span> Question $questionNumber: {$detail['question_text']}</div>";
                $successMessage .= "<div class='answer'>Your answer: {$detail['user_answer_text']}</div>";

                // Always show the correct answer for incorrect responses
                if (!$detail['is_correct']) {
                    $successMessage .= "<div class='answer correct'>Correct answer: {$detail['correct_answer']}</div>";
                }

                $successMessage .= "</div>";
            }

            $successMessage .= "</div>";

            if ($req->ajax()) {
                return response()->json(['success' => $successMessage]);
            } else {
                return redirect()->route('reviewPage', ['id' => $bookId, 'slug' => $slug])->with('success', $successMessage);
            }
        } catch (\Exception $e) {
            \Log::error('Error processing quiz submission: ' . $e->getMessage(), [
                'correctAnswer' => $totalCorrectAnswers,
                'bookId' => $bookId
            ]);
            return response()->json(['error' => 'An error occurred while processing the quiz submission'], 500);
        }
    }

    /**
     * Handle submission of valid Amazon review links by users
     */
    public function submitLink(Request $request)
    {
        $request->validate([
            'review_id' => 'required|exists:reviews,id',
            'amazon_link' => 'required|url'
        ]);

        $userId = Auth::user()->id;
        $reviewId = $request->review_id;
        $amazonLink = $request->amazon_link;

        // Validate that the link is actually an Amazon link
        if (!str_contains(strtolower($amazonLink), 'amazon.')) {
            return response()->json([
                'success' => false,
                'message' => 'Please provide a valid Amazon review link.'
            ], 400);
        }

        // Get the review to ensure it belongs to the current user and is rejected
        $review = Review::where('id', $reviewId)
            ->where('userid', $userId)
            ->where('reviewStatus', 3) // Only allow for rejected reviews
            ->first();

        if (!$review) {
            return response()->json([
                'success' => false,
                'message' => 'Review not found or not eligible for link submission.'
            ], 404);
        }

        // Check if a link has already been submitted for this review
        $existingSubmission = SubmittedLink::where('review_id', $reviewId)->first();
        if ($existingSubmission) {
            return response()->json([
                'success' => false,
                'message' => 'A link has already been submitted for this review.'
            ], 400);
        }

        // Create the submitted link record
        SubmittedLink::create([
            'review_id' => $reviewId,
            'user_id' => $userId,
            'book_id' => $review->bookId,
            'submitted_link' => $amazonLink
        ]);

        // Create an activity record for the user
        $activity = new Activity();
        $activity->userid = $userId;
        $activity->bookId = $review->bookId;
        $activity->reason = "You submitted a valid Amazon review link for \"{$review->book->title}\". Our team will review it shortly.";
        $activity->notification_type = 'reader';
        $activity->save();

        return response()->json([
            'success' => true,
            'message' => 'Link submitted successfully.'
        ]);
    }
}
