@extends('master-layout.master-layout');
@php
    use App\Models\SystemControl;
@endphp
@section('page_content')
@php

if ($advertising) {
$advertisingPoints = $advertising->bookPrice;
$totalPoints = 0;

switch ($advertisingPoints) {
case 0.99:
$totalPoints = 100;
break;
case 1.99:
$totalPoints = 300;
break;
case 2.99:
$totalPoints = 600;
break;
case 3.99:
$totalPoints = 900;
break;
case 4.99:
$totalPoints = 1200;
break;
case 5.99:
$totalPoints = 1500;
break;
case 6.99:
$totalPoints = 1800;
break;
case 7.99:
$totalPoints = 2100;
break;
case 8.99:
$totalPoints = 2400;
break;
case 9.99:
$totalPoints = 2700;
break;
case 0:
$totalPoints = 50;
break;
}
} else {
$totalPoints = 0;
}
@endphp

<div class="page-content-container reader-container">
    <section class="reader-hero-section bg-gray d-flex align-items-center justify-content-center reader-padding-medium pb-5">
        <div class="hero-content">
            <div class="container">
                <div class="row">
                    <div class="text-center reader-padding-medium reader-no-padding-bottom">
                        @php
                            $quote = App\Helpers\ImageHelper::getRandomQuote();
                            $heroImage = App\Helpers\ImageHelper::getRandomImage('2.Horizontal Small');
                        @endphp
                        <div class="d-flex justify-content-center reader-filter-container">
                            <div class="reader-hero-image-container" style="max-width: 240px;">
                                <img src="{{ $heroImage['url'] }}" alt="{{ $heroImage['filename'] }}" class="img-fluid">
                                <div class="reader-image-caption">{{ $heroImage['filename'] }}</div>
                            </div>
                        </div>
                        <blockquote class="reader-literary-quote">
                            {!! $quote !!}
                        </blockquote>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!-- Flash messages are now handled by master layout -->
    <section class="padding-medium ">
        <div class="container bg-light p-4">
            <div class="row d-flex justify-content-center align-items-center">
                <div class="col-md-3">
                    <img class="img-fluid" src="{{ asset('Books/book_cover/') }}{{ '/' . $book->front_book_cover }}" alt="">
                    <h5 class="text-center mt-4">Reward Points: {{ $totalPoints }}</h5>
                </div>
                <div class="col-md-9">
                    <div class="row">
                        <div class="col-md-12">
                            <h2>{{ $book->title }}</h2>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12">
                            <p>{{ $book->book_summary }}</p>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12">
                            @if($reader && $reader->type == 'KDP')
                                <p>Book Type: Kindle Unlimited</p>
                            @elseif($reader && $reader->type == 'Free')
                                <p>Book Type: Free</p>
                            @elseif($reader && $reader->type == 'Paid')
                                <p>Book Type: Verified Purchase</p>
                                @if($advertising && is_numeric($advertising->bookPrice))
                                    <p>${{ number_format($advertising->bookPrice, 2) }}</p>
                                    <div class="alert alert-info">
                                        <p>You must BUY this book on Amazon for your review to be stamped as Verified Purchase</p>
                                    </div>
                                @endif
                            @else
                                @if($advertising && $advertising->bookPrice == 'KU')
                                    <p>Book Type: Kindle Unlimited</p>
                                @elseif($advertising && $advertising->bookPrice == 0)
                                    <p>Book Type: Free</p>
                                @else
                                    <p>Book Type: Verified Purchase</p>
                                    @if($advertising && is_numeric($advertising->bookPrice))
                                        <p>${{ number_format($advertising->bookPrice, 2) }}</p>
                                        <div class="alert alert-info">
                                            <p>You must BUY this book on Amazon for your review to be stamped as Verified Purchase</p>
                                        </div>
                                    @endif
                                @endif
                            @endif

                            <p>Word Count: {{ number_format($book->wordCount ?? 0) }}</p>
                            <p>Turnaround Time: {{ $advertising->TurnAroundTime }} days</p>
                            <p>Genre: {{ $book->categories->category }}</p>
                            @php
                                $reviews = $book->reviews;
                                $totalRating = 0;
                                $totalReviews = $reviews->count();
                                foreach ($reviews as $review) {
                                    $totalRating += $review->rating;
                                }
                                $averageRating = $totalReviews > 0 ? $totalRating / $totalReviews : 0;
                            @endphp
                            <p>Current Rating: {{ $totalReviews > 0 ? \App\Helpers\NumberHelper::formatRating($averageRating) . ' Stars' : 'No Rating' }}</p>
                        </div>
                    </div>
                    <div class="row mt-5">
                        <div class="col-md-12">
                            <a href="{{ $book->book_amazon_url }}" class="btn btn-text-link" id="downloadBtn">View On Amazon</a>

                            @if ($reader == '')
                            <form action="{{ route('book.assign', $book->slug) }}" method="POST" style="display:inline">
                                @csrf
                                <input type="hidden" name="startDate" id="startDate">
                                <input type="hidden" name="endDate" id="endDate">
                                <input type="hidden" value="1" name="status" id="status">
                                <button class="btn btn-text-link">Get This Book</button>
                            </form>
                            @else
                            {{-- <a href="{{ route('reader') }}" class="btn btn-text-link">View In Readers Page</a> --}}
                            @endif
                            <a class="btn btn-text-link" href="{{ asset('books/book_pdf') }}/{{ $book->book_pdf }}" download="{{ $book->title }}" id="">Download Book</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Display quiz results here if available -->
    @if (Session::has('success') && strpos(Session::get('success'), 'quiz-results') !== false)
    <section class="reader-padding-medium quiz-results-section">
        <div class="container bg-light p-4">
            {!! Session::get('success') !!}
        </div>
    </section>
    @endif

    <section class="reader-padding-medium timeline">
        <div class="container bg-light p-4">
            <h3 class="page_section_heading reader-section-heading">Your Timeline</h3>
            <p class="about-text">You Have <span id="days"></span> {{ $displayTime }}</p>
            <form action="{{ route('cancelAssignment', $book1->id) }}" method="POST">
                @csrf
                <input type="submit" value="Cancel Assignment" class="btn btn-text-link">
            </form>
        </div>
    </section>

    <section class="reader-padding-medium turn-in-review">
        <div class="container bg-light p-4">
            <form action="{{ route('post.review') }}" method="POST">
                @csrf
                <div class="quiz-cont">
                    <input type="hidden" name="totalMcqsMarks">
                </div>
                <div class="main-review-cont">
                    <div class="row">
                        <div class="col-md-12">
                            <h3 class="page_section_heading reader-section-heading">Turn In Review</h3>
                            <div class="row">
                                <div class="col-md-12 rating-div mb-3 review-radio-group">
                                    <input type="hidden" name="bookId" value="{{ $book1->id }}">
                                    <small class="about-text">Your Rating:</small>
                                    <label for="rating-1">
                                        <input type="radio" id="rating-1" name="rating" value="1">
                                        <span class="about-text">1 Star</span>
                                    </label>
                                    <label for="rating-2">
                                        <input type="radio" id="rating-2" name="rating" value="2">
                                        <span class="about-text">2 Star</span>
                                    </label>
                                    <label for="rating-3">
                                        <input type="radio" id="rating-3" name="rating" value="3">
                                        <span class="about-text">3 Star</span>
                                    </label>
                                    <label for="rating-4">
                                        <input type="radio" id="rating-4" name="rating" value="4">
                                        <span class="about-text">4 Star</span>
                                    </label>
                                    <label for="rating-5">
                                        <input type="radio" id="rating-5" name="rating" value="5">
                                        <span class="about-text">5 Star</span>
                                    </label>
                                </div>
                            </div>
                            <div class="row">
                                @if(Auth::user()->amazon_reviewer_name && !$errors->has('amazon_reviewer_name'))
                                    <h6 class="about-text">Dear {{ Auth::user()->fullName }}, please check the Number Of Stars You rated This Book in Your Amazon Review under {{ Auth::user()->amazon_reviewer_name }} Reviewer Name</h6>
                                @elseif(!$errors->has('amazon_reviewer_name') && !Auth::user()->amazon_reviewer_name)
                                    <div class="alert alert-warning">
                                        Dear {{ Auth::user()->fullName }}, please fill in your Amazon Reviewer Name in <a href="{{ route('profile.index') }}">User's Profile</a>
                                    </div>
                                @endif

                                @error('rating')
                                    <div class="alert alert-danger mt-2">
                                        {{ $message }}
                                    </div>
                                @enderror

                                @error('amazon_reviewer_name')
                                    <div class="alert alert-danger mt-2">
                                        Please fill in your Amazon Reviewer Name in <a href="{{ route('profile.index') }}">User's Profile</a>
                                    </div>
                                @enderror
                            </div>
                            <!-- Replace hidden review field with Notice to Fellow Auteur textarea -->
                            <div class="row mt-3">
                                <div class="col-md-12">
                                    <label for="review" class="review-text">
                                        <strong>A Notice to a Fellow Auteur:</strong><br>
                                        Dear Auteur, you may leave a Helpful Notice to a Book's Author with an anonymous advice on book and craft.
                                        If appreciated, you will receive up to {{ SystemControl::where('key_type', 'notice_reward_3')->value('value') }} bonus Rewards!
                                        Please Help Your Fellow Auteur with a straightforward yet truthful and candid advice on how to become even a better author!
                                        <br><small class="text-muted">(please be kind, your notices are moderated;)</small>
                                    </label>
                                    <textarea name="review" id="review" class="form-control mt-2" rows="4" placeholder="Write your notice here..."></textarea>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <input type="hidden" name="points" value='{{ $totalPoints }}'>
                            <input type="hidden" name="type" value="{{ $reader ? $reader->type : 'Free' }}">
                            <input type="hidden" name="price" value="{{ $advertising ? $advertising->bookPrice : 0 }}">
                            <input type="submit" class="btn btn-text-link" value="Submit Review">
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </section>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            @if($errors->has('rating') || $errors->has('amazon_reviewer_name'))
                // Scroll to the turn-in-review section when validation errors occur
                document.querySelector('.turn-in-review').scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            @endif
        });
    </script>

    {{-- <section class="padding-medium assignment">
        <div class="container bg-light p-5">
            <h2>Your Assignment</h2>
            <div class="row mt-4">
                <div class="col-md-6">
                    <form action="">
                        <table class="table" border="1">
                            <tbody>
                                <tr>
                                    <td>Downloaded</td>
                                    <td><input class="form-checkbox" type="checkbox" name="" id=""></td>
                                </tr>
                                <tr>
                                    <td>Read</td>
                                    <td><input type="checkbox" name="" id=""></td>
                                </tr>
                                <tr>
                                    <td>Review Post</td>
                                    <td><input type="checkbox" name="" id=""></td>
                                </tr>
                            </tbody>
                        </table>
                        <input type="submit" value="save" class="btn btn-primary">
                    </form>
                    <p>Tick All The Boxes And Click Save To Share Your Progress</p>

                </div>
                <div class="col-md-6">
                    <div class="row gy-3  justify-content-center align-items-center">
                        <div class="col-md-12">
                            <a class="text-center d-block fs-4"
                                href="{{ asset('books/book_pdf') }}/{{ $book->book_pdf }}"
                                download="{{ $book->title }}">Download Book</a>
                        </div>
                        <div class="col-md-12">
                            <a class="text-center d-block fs-4" href="{{ $book->book_amazon_url }}">View Book On
                                Amazon</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
</section> --}}

    <section class="reader-padding-medium">
        <div class="container bg-light p-4">
            <h3 class="page_section_heading reader-section-heading">About This Book</h3>
            <div class="book-meta mt-5">
                <div>
                    <h5 class="about-heading">Favorite excerpt(s) from the book</h5>
                    <p class="about-text">{{ $book->favorite_excerpts }}</p>
                </div>
                {{-- Removed unique aspects section --}}
                {{-- Removed book phrases section --}}
                {{-- Removed enjoy most section --}}
            </div>
        </div>
    </section>

</div>
@endsection



@section('additionalScript')
<script>
    // Get all elements with the class 'trimthisWord'
    var phrasesElements = document.querySelectorAll('.trimthisWord');

    // Loop through each element and modify its content
    phrasesElements.forEach(function(element) {
        var dataFromDatabase = element.textContent;

        // Split the data by commas and join with hyphens and line breaks
        var dataItems = dataFromDatabase.split(',');
        var formattedData = dataItems.map(function(item, index) {
            var capitalizedItem = item.trim().charAt(0).toUpperCase() + item.trim().slice(1);
            return (index + 1) + '. ' + capitalizedItem;
        }).join('<br>');

        // Update the content of the element
        element.innerHTML = formattedData;

        var currentDate = new Date();
        var formattedDate = currentDate.getFullYear() + '-' + (currentDate.getMonth() + 1) + '-' + currentDate
            .getDate();
        $('#startDate').val(formattedDate);

        var daysToAdd = parseInt({
            {
                $book - > preferred_days
            }
        });
        var endDate = new Date(currentDate);
        endDate.setDate(currentDate.getDate() + daysToAdd);
        var formattedEndDate = endDate.getFullYear() + '-' + (endDate.getMonth() + 1) + '-' +
            endDate.getDate();
        $('#endDate').val(formattedEndDate);
    });

    $(document).ready(function() {
        var endDateStr = '{{ $reader->endDate }}';
        console.log(endDateStr);
        if (endDateStr) {
            var endDate = new Date(endDateStr);
            var today = new Date();
            var timeDifference = endDate - today;
            var dayDifference = Math.floor(timeDifference / (1000 * 60 * 60 * 24));
            $('#days').html(dayDifference);
        }
    });

</script>


<script>
    $(document).ready(function() {
        $("#downloadBtn").on("click", function(e) {
            e.preventDefault(); // Prevent the default behavior of the link

            // Copy the link to the clipboard
            var linkToCopy = $(this).attr("href");
            var tempInput = $("<input>");
            $("body").append(tempInput);
            tempInput.val(linkToCopy).select();
            document.execCommand("copy");
            tempInput.remove();

            // Show the alert
            alert("Link copied to clipboard: " + linkToCopy);
        });
    });

</script>
@endsection
