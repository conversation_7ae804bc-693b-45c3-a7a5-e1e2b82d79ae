<?php

namespace App\Helpers;

class NumberHelper
{
    /**
     * Format a number to one decimal place
     * If the number is an integer (e.g., 4.0), show it without decimal places (e.g., '4')
     * Otherwise, show one decimal place (e.g., '4.3')
     *
     * @param float|int|string $number
     * @return string
     */
    public static function formatToOneDecimal($number)
    {
        if ($number === null || $number === '') {
            return '0';
        }

        $number = (float) $number;
        $rounded = round($number, 1);
        
        // If the number is an integer (e.g., 4.0), show it without decimal places
        if ($rounded == (int) $rounded) {
            return (string) (int) $rounded;
        }
        
        // Otherwise, show one decimal place
        return number_format($rounded, 1);
    }

    /**
     * Format a balance/wallet amount to one decimal place
     *
     * @param float|int|string $balance
     * @return string
     */
    public static function formatBalance($balance)
    {
        return self::formatToOneDecimal($balance);
    }

    /**
     * Format a rating to one decimal place
     *
     * @param float|int|string $rating
     * @return string
     */
    public static function formatRating($rating)
    {
        return self::formatToOneDecimal($rating);
    }

    /**
     * Format quiz results to one decimal place
     *
     * @param float|int|string $score
     * @return string
     */
    public static function formatQuizScore($score)
    {
        return self::formatToOneDecimal($score);
    }
}
