/* Alert Overrides - These styles take precedence over all other alert styles */
.alert {
    border: none !important;
    border-radius: 0 !important;
    box-shadow: none !important;
    background-image: none !important;
    margin: 10px 0 !important;
    padding: 15px !important;
    font-weight: 300 !important;
    font-size: 14px !important;
}

.alert-success {
    background-color: #d4edda !important; /* Light green background */
    color: #28a745 !important; /* Green text for success */
}

.alert-danger {
    background-color: #f8d7da !important; /* Light red background */
    color: #dc3545 !important; /* Red text for errors */
}

.alert-info {
    background-color: #e6f2ff !important; /* Pale blue background */
    color: #17a2b8 !important; /* Blue text for info */
}

.alert-warning {
    background-color: #fff3cd !important; /* Light yellow background */
    color: #856404 !important; /* Dark brown text for better readability on yellow background */
}

/* Remove any potential Bootstrap or other framework styling */
.alert::before,
.alert::after {
    display: none !important;
}

/* Alert block class - used in some templates */
.alert-block {
    border: none !important;
    background-image: none !important;
}

/* Ensure alert-block inherits the correct background colors */
.alert-success.alert-block {
    background-color: #d4edda !important;
}

.alert-danger.alert-block {
    background-color: #f8d7da !important;
}

.alert-info.alert-block {
    background-color: #e6f2ff !important;
}

.alert-warning.alert-block {
    background-color: #fff3cd !important;
}

/* Quiz results specific overrides */
.quiz-results {
    background-color: #d4edda !important; /* Match success message background */
    border: none !important;
    box-shadow: none !important;
}

.quiz-results .question {
    border-left: none !important;
}

.quiz-results .summary {
    border-bottom: none !important;
}
