@extends('master-layout.master-layout')
@section('page_title', $book->title)
@section('page_content')
    <style>
        /* Author book view specific styles */
        .author-literary-quote {
            margin-top: 10px !important;
            margin-bottom: 20px;
        }

        /* Ensure the hero image container has the correct width */
        .author-hero-image-container {
            width: 240px;
        }

        /* Using the global page-submenu styles from auteurs-master.css */
    </style>
    <div class="page-content-container author-container">
        <!-- Page Submenu - Placed right after the page title -->
        <div class="page-submenu text-center">
            <div class="container">
                <div class="row">
                    <div class="col-12">
                        <ul class="nav justify-content-center">
                            <li class="nav-item">
                                <a class="nav-link" href="#review-options">Review Options</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="#reviews">Reviews History</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="#friendly-notes">Friendly Notes from Fellow Auteurs</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="#activity">Activity</a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <section class="hero-section author-hero-section bg-gray d-flex align-items-center justify-content-center author-padding-medium pb-5">
            <div class="hero-content">
                <div class="container">
                    <div class="row">
                        <div class="text-center author-padding-medium author-no-padding-bottom">
                            <!-- <h1 class="author-book-title"><span class="highlighted-title">{{ $book->title }}</span></h1> -->
                            @php
                                $quote = App\Helpers\ImageHelper::getRandomQuote();
                                $heroImage = App\Helpers\ImageHelper::getRandomImage('2.Horizontal Small');
                            @endphp
                            <div class="d-flex justify-content-center author-filter-container">
                                <div class="author-hero-image-container">
                                    <img src="{{ $heroImage['url'] }}" alt="{{ $heroImage['filename'] }}" class="img-fluid">
                                    <div class="author-image-caption">{{ $heroImage['filename'] }}</div>
                                </div>
                            </div>
                            <blockquote class="author-literary-quote">
                                {!! $quote !!}
                            </blockquote>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <div class="container">
            <section class="padding-medium">
                <h3 class="activity-heading section-title">Your Book</h3>
                <div class="row author-book-card">
                    <div class="col-md-4">
                        <img src="{{ asset('Books/book_cover/') }}{{ '/' . $book->front_book_cover }}" alt=""
                            class="img-fluid">
                        <div class="row">
                            <div class="col-md-6 {{ $book->approval_status == 3 ? 'col-md-12' : 'col-md-6' }}">
                                <a href="#review-options"
                                    class="btn btn-text-link d-block mt-2">Get
                                    Review</a>
                            </div>
                            @if ($book->approval_status != 3)
                                <div class="col-md-6">
                                    <a href="{{ route('authorEdit.single', $book->slug) }}"
                                        class="btn btn-text-link d-block mt-2">Edit Book</a>
                                </div>
                            @endif
                        </div>
                    </div>
                    <div class="col-md-8">
                        <h4>{{ $book->title }}</h4>
                        <p class="book-summary">{{ $book->book_summary }}</p>
                        <div class="rating">
                            @php
                                $roundedRating = round($averageRating); // Round the average rating to the nearest whole number
                            @endphp

                            @for ($i = 1; $i <= $roundedRating; $i++)
                                <i class="ri-star-fill colored"></i>
                            @endfor

                        </div>
                        @php
                            if ($book->approval_status == 1) {
                                $status = '<span class="text-success">Active</span>';
                            } elseif ($book->approval_status == 2) {
                                $status = '<span class="text-danger">Changes Required</span>';
                            } else {
                                $status = '<span class="text-warning">Pending Approval</span>';
                            }
                        @endphp
                        <p>Book Status: <b>{!! $status !!}</b></p>
                        <div class="meta-data">
                            <p>Looking for <b>{{ $totalBookReadersRequired }}</b> Readers</p>
                            <p><b>{{ $countReaders }}</b> Reader is Reading</p>
                            <p><b>{{ $averageRating == (int)$averageRating ? (int)$averageRating : number_format($averageRating, 1) }}</b> Stars</p>
                            <p><b>{{ $reviews->count() }}</b> Reviews History</p>
                        </div>
                    </div>

                </div>
            </section>
            <section id="review-options" class="padding-medium bg-light p-5">
                <h3 class="activity-heading section-title">Review Options</h3>

                @if ($book->approval_status == 0 or $book->approval_status == 2 or $book->approval_status == 3)
                    <p class="activity-text">Once Your Book is Published You Will Be Able To Boost The Book</p>
                @else
                    {{-- {{ $data }} --}}
                    <form action="{{ route('boost.book') }}" method="post" class="form">
                        @csrf
                        <input type="hidden" name="bookId" value="{{ $book->id }}">
                        <div class="from-group mt-5">
                            <label for="" class="activity-text">Turnaround Time</label>
                            <input type="hidden" id="initialPoints" value="0">
                            <div class="row">
                                <div class="turnaround-radio-group">
                                    @foreach ($turnArounodTime as $time)
                                        <label for="{{ $time->key }}">
                                            <input value="{{ $loop->index + 1 }}" type="radio" id="{{ $time->key }}"
                                                {{ $time->key == '4 Days' ? 'checked' : '' }} data-price="{{ $time->value }}"
                                                name="turnAroundTime">
                                            <span>{{ $time->key }}</span>
                                        </label>
                                    @endforeach
                                </div>

                                <div class="col-md-12">
                                    <small class="activity-text">Choose the number of days your reader is given to read your book and turn in their
                                        review</small>
                                    <p class="recommended-days">The Recommended Days Based On Your Page Length: {{ $book->preferred_days }} Days</p>
                                </div>
                            </div>
                        </div>
                        <div class="form-group mt-4">
                            <label for="" class="activity-text">Verified Purchase</label>
                            <input type="checkbox" name="verifiedPurchase" class="review-options-checkbox">
                            <p class="activity-text">Your Reader Will Purchase your book and leave a Verified Purchase Review</p>

                            <div class="pricingdiv turnaround-radio-group">
                                @foreach ($virtualPoints as $vp)
                                    <label for="{{ $vp->key }}">
                                        <input type="radio" id="{{ $vp->key }}"
                                            value="{{ $vp->key }}" data-price="{{ $vp->value }}"
                                            name="bookPrice">
                                        <span>{{ $vp->key }}</span>
                                    </label>
                                @endforeach
                            </div>
                        </div>

                        <div class="form-group mt-4">
                            <label for="" class="activity-text">Kindle Unlimited</label>
                            <input type="checkbox" id="kindleUnlimitedCheckbox" name="KindleUnlimited" class="review-options-checkbox">
                            <p class="activity-text">If Your Book Is Kindle Unlimited Please Check Mark This Box.</p>
                        </div>

                        <div class="form-group mt-5">
                            <div class="totalRewards">
                                <b>Total</b>
                                <input type="hidden" name="points" id="total">
                                <h5><span id="totalPoints">{{ $book->BasicPoints }}</span> Rewards</h5>
                            </div>
                        </div>
                        <div class="form-group">
                            @if($isBlockedFromFeaturing)
                                <button type="button" class="btn btn-sm btn-secondary" disabled style="font-size: 1.0rem !important;">Feature a Book!</button>
                                <div class="BookFieldsExplain mt-2">
                                    {!! $featuringBlockReason !!}
                                </div>
                            @else
                                <button type="submit" class="btn btn-sm btn-primary" style="font-size: 1.0rem !important;">Feature a Book!</button>
                            @endif
                        </div>
                    </form>

                @endif
            </section>

            <section id="reviews" class="padding-medium">
                <h3 class="activity-heading section-title">Reviews History</h3>

                @if ($reviews->count() > 0)
                    <div class="reviews-container">
                        <div class="reviews-list-container truncated-reviews">
                            @foreach($reviews as $index => $review)
                            <div class="review-history-item" data-review-index="{{ $index }}">
                                <div class="review-history-content">
                                    <div class="review-history-title">
                                        <!-- Rating -->
                                        <div class="d-flex mb-2">
                                            @for($i = 1; $i <= 5; $i++)
                                                @if($i <= $review->rating)
                                                    <i class="ri-star-fill" style="color: #ffd700;"></i>
                                                @else
                                                    <i class="ri-star-line" style="color: #ffd700;"></i>
                                                @endif
                                            @endfor
                                        </div>
                                    </div>

                                    <div class="review-history-summary">
                                        <!-- Validation Date -->
                                        <p>Validation Date: 
                                            {{ $review->updated_at && in_array($review->reviewStatus, [1, 2]) ? 
                                               \Carbon\Carbon::parse($review->updated_at)->format('M d, Y') : 'N/A' }}
                                        </p>

                                        <!-- Amazon Link -->
                                        @if(!empty($review->review_link) && in_array($review->reviewStatus, [1, 2]))
                                            <a href="{{ $review->review_link }}" class="btn btn-text-link" target="_blank">
                                                View on Amazon
                                            </a>
                                        @endif
                                    </div>

                                    <div class="review-history-date">
                                        <!-- Status -->
                                        @if($review->reviewStatus == 1)
                                            <span class="badge bg-success">Validated</span>
                                        @elseif($review->reviewStatus == 2)
                                            <span class="badge bg-warning">Approved Partially</span>
                                            @if($review->type == 'Paid')
                                                <div class="small text-warning mt-1">VP review not stamped as VP</div>
                                            @endif
                                        @elseif($review->reviewStatus == 0)
                                            <span class="badge bg-secondary">Pending</span>
                                        @endif
                                    </div>
                                </div>
                            </div>
                            @endforeach
                        </div>
                        @if ($reviews->count() > 4)
                            <div class="reviews-show-more-wrapper" style="margin-top: 10px;">
                                <a class="learn-more-toggle reviews-toggle" style="color: var(--accent-color) !important; text-decoration: underline !important; cursor: pointer !important; display: inline-block !important; background: none !important; border: none !important; padding: 0 !important; font-size: 14px !important; font-weight: normal !important;">Show More</a>
                            </div>
                        @endif
                    </div>
                @else
                    <div class="mt-4">
                        <div class="review-history-item">
                            <div class="review-history-content">
                                <p class="text-center">No reviews yet.</p>
                            </div>
                        </div>
                    </div>
                @endif
            </section>

            <section id="friendly-notes" class="padding-medium">
                <h3 class="activity-heading section-title">Friendly Notes from Fellow Auteurs</h3>

                @php
                    // Filter reviews to only include those with actual content
                    $reviewsWithContent = $reviews->filter(function($review) {
                        return $review->review !== 'No Content' && !empty(trim($review->review));
                    });
                @endphp

                @if ($reviewsWithContent->count() > 0)
                    <div class="notes-container">
                        <div class="notes-list-container truncated-notes">
                            @foreach($reviewsWithContent as $index => $review)
                                <div class="note-card p-4 mb-4 border rounded bg-light" data-note-index="{{ $index }}">
                                    <div class="note-date text-muted mb-2">{{ $review->created_at->format('M d, Y') }}</div>

                                    @php
                                        $reviewContent = $review->review;
                                        // Split content by newlines
                                        $lines = explode("\n", $reviewContent);
                                        // Count real lines (ignore empty ones)
                                        $realLines = array_filter($lines, function($line) {
                                            return trim($line) !== '';
                                        });
                                        // Reindex array after filtering
                                        $realLines = array_values($realLines);
                                        // Get line count for determining if show more is needed
                                        $lineCount = count($realLines);
                                        $hasMoreContent = $lineCount > 3;

                                        // Get first 3 lines only for initial display
                                        $visibleLines = array_slice($realLines, 0, 3);
                                        $visibleContent = implode("\n", $visibleLines);

                                        // Get remaining lines for hidden content
                                        $hiddenLines = $hasMoreContent ? array_slice($realLines, 3) : [];
                                        $hiddenContent = implode("\n", $hiddenLines);
                                    @endphp

                                    <div class="note-content">
                                        <div class="visible-content">
                                            {!! nl2br(e($visibleContent)) !!}
                                        </div>

                                        @if($hasMoreContent)
                                            <div class="note-hidden-content" style="display: none;">
                                                {!! nl2br(e($hiddenContent)) !!}
                                            </div>
                                            <div class="show-more-button-wrapper" style="margin-top: 5px; margin-bottom: 10px;">
                                                <button class="learn-more-toggle note-content-toggle" style="color: var(--accent-color) !important; text-decoration: underline !important; cursor: pointer !important; display: inline-block !important; background: none !important; border: none !important; padding: 0 !important; font-size: 14px !important; font-weight: normal !important; opacity: 1 !important; visibility: visible !important;">Show more</button>
                                            </div>
                                        @endif
                                    </div>

                                    <div class="reward-options mt-3">
                                        <p class="mb-2">You may return your thanks to a fellow Auteur (if you find it helpful):</p>

                                        <div class="reward-buttons">
                                            @if(!$review->notice_tipped)
                                                @php
                                                    $r1 = \App\Models\SystemControl::where('key_type', 'notice_reward_1')->value('value');
                                                    $r2 = \App\Models\SystemControl::where('key_type', 'notice_reward_2')->value('value');
                                                    $r3 = \App\Models\SystemControl::where('key_type', 'notice_reward_3')->value('value');
                                                    $r0 = \App\Models\SystemControl::where('key_type', 'notice_fee')->value('value') ?? 2;
                                                @endphp
                                                <button class="btn btn-sm btn-outline-secondary mr-2 no-need-button" data-review-id="{{ $review->id }}">
                                                    No need
                                                </button>
                                                <button class="btn btn-sm btn-outline-primary mr-2 tip-button" data-review-id="{{ $review->id }}" data-reward-option="notice_reward_1">
                                                    {{ $r1 }} Rewards
                                                </button>
                                                <button class="btn btn-sm btn-outline-primary mr-2 tip-button" data-review-id="{{ $review->id }}" data-reward-option="notice_reward_2">
                                                    {{ $r2 }} Rewards
                                                </button>
                                                <button class="btn btn-sm btn-outline-primary tip-button" data-review-id="{{ $review->id }}" data-reward-option="notice_reward_3">
                                                    {{ $r3 }} Rewards
                                                </button>
                                            @else
                                                @if($review->notice_tip_amount > 0)
                                                    <div class="text-success">
                                                        <i class="ri-checkbox-circle-line"></i> You've already tipped this notice with {{ $review->notice_tip_amount }} Rewards.
                                                    </div>
                                                @else
                                                    <div class="text-primary">
                                                        <i class="ri-information-line"></i> You've decided not to tip this Notice with Rewards.
                                                    </div>
                                                @endif
                                            @endif
                                        </div>

                                        @if(!$review->notice_tipped)
                                            <p class="text-muted small mt-2 fee-notice">(whatever Reward you choose, it will cost you only {{ $r0 ?? 2 }} Reward points; please also report any abusive message!)</p>
                                        @endif
                                    </div>
                                </div>
                            @endforeach
                        </div>
                        @if ($reviewsWithContent->count() > 4)
                            <div class="notes-show-more-wrapper" style="margin-top: 10px;">
                                <a class="learn-more-toggle notes-toggle" style="color: var(--accent-color) !important; text-decoration: underline !important; cursor: pointer !important; display: inline-block !important; background: none !important; border: none !important; padding: 0 !important; font-size: 14px !important; font-weight: normal !important;">Show More</a>
                            </div>
                        @endif
                    </div>
                @else
                    <p class="text-center">No notes from fellow auteurs yet.</p>
                @endif
            </section>

            <section id="activity" class="padding-medium">
                <h3 class="activity-heading section-title">Activity</h3>

                @if ($activity == null || $activity->count() == 0)
                    <p class="activity-text">No Activity Yet</p>
                @else
                    <div class="activity-container">
                        <div class="activity-table-container">
                            <table class="table border table-stripped">
                                <thead>
                                    <tr>
                                        <th class="table-header">Date</th>
                                        <th class="table-header">Activity</th>
                                    </tr>
                                </thead>
                                <tbody class="activity-tbody truncated-activity">
                                    @foreach ($activity as $index => $item)
                                        <tr class="activity-row" data-activity-index="{{ $index }}">
                                            <td class="activity-text">{{ $item->created_at }}</td>
                                            <td class="activity-text">{{ $item->reason }}</td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                        @if ($activity->count() > 10)
                            <div class="activity-show-more-wrapper" style="margin-top: 10px;">
                                <a class="learn-more-toggle activity-toggle" style="color: var(--accent-color) !important; text-decoration: underline !important; cursor: pointer !important; display: inline-block !important; background: none !important; border: none !important; padding: 0 !important; font-size: 14px !important; font-weight: normal !important;">Show More</a>
                            </div>
                        @endif
                    </div>
                @endif
            </section>
        </div>
        <input type="hidden" id="basicPoints" value="{{ $book->BasicPoints }}">
        <input type="hidden" id="KindlePoints" value="{{ $KindlePoints->value }}">
    </div>
@endsection


@section('additionalScript')

    <script src="https://code.jquery.com/jquery-3.7.1.min.js"
        integrity="sha256-/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=" crossorigin="anonymous"></script>
    <script>
        $(document).ready(function() {
            // Selecting elements
            var turnAroundTime = $("input[name='turnAroundTime']");
            var bookPrice = $("input[name='bookPrice']");
            var verifiedPurchase = $("input[name='verifiedPurchase']");
            var kindleUnlimited = $("input[name='KindleUnlimited']");
            var total = $('#total');
            var displayPoints = $('#totalPoints');
            var basicPoints = parseInt($('#basicPoints').val()) || 0; // Ensure basicPoints is an integer
            var pricingDiv = $('.pricingdiv');

            // Retrieve Kindle Unlimited points from the element with ID KindlePoints
            var kindlePoints = parseInt($('#KindlePoints').val()) || 0;

            // Initialize pricing div visibility based on checkbox state
            if (verifiedPurchase.prop('checked')) {
                pricingDiv.addClass('show');
            } else {
                pricingDiv.removeClass('show');
            }

            // Function to update display points
            function updateDisplayPoints() {
                var points = basicPoints;

                if (verifiedPurchase.prop('checked')) {
                    pricingDiv.addClass('show');
                    bookPrice.each(function() {
                        if ($(this).prop('checked')) {
                            var selectedPrice = parseInt($(this).attr('data-price')) || 0;
                            points += selectedPrice;
                        }
                    });
                } else {
                    pricingDiv.removeClass('show');
                    bookPrice.prop('checked', false); // Uncheck all book prices
                }

                if (kindleUnlimited.prop('checked')) {
                    points += kindlePoints;
                }

                turnAroundTime.each(function() {
                    if ($(this).prop('checked')) {
                        var turnAroundTimeVal = parseInt($(this).attr('data-price')) || 0;
                        points += turnAroundTimeVal;
                    }
                });

                displayPoints.text(points);
                total.val(points);
            }

            // Event handlers
            verifiedPurchase.on('change', function() {
                if ($(this).prop('checked')) {
                    kindleUnlimited.prop('checked', false);
                    pricingDiv.addClass('show');
                } else {
                    pricingDiv.removeClass('show');
                    // Uncheck all book prices and update points accordingly
                    bookPrice.prop('checked', false);
                }
                updateDisplayPoints();
            });

            kindleUnlimited.on('change', function() {
                if ($(this).prop('checked')) {
                    verifiedPurchase.prop('checked', false);
                    pricingDiv.removeClass('show');
                    bookPrice.prop('checked', false); // Uncheck all book prices
                }
                updateDisplayPoints();
            });

            bookPrice.on('change', function() {
                updateDisplayPoints();
            });

            turnAroundTime.on('change', function() {
                updateDisplayPoints();
            });

            // Initialize display points
            updateDisplayPoints();

            // Handle show more button for all sections
            $('.learn-more-toggle').on('click', function(e) {
                e.preventDefault();
                var $this = $(this);
                
                // Check if this is an activity toggle
                if ($this.hasClass('activity-toggle')) {
                    var $activityTbody = $this.closest('.activity-container').find('.activity-tbody');
                    
                    if ($activityTbody.hasClass('expanded')) {
                        $activityTbody.removeClass('expanded');
                        $this.text('Show More');
                    } else {
                        $activityTbody.addClass('expanded');
                        $this.text('Show Less');
                    }
                } 
                // Check if this is a reviews toggle
                else if ($this.hasClass('reviews-toggle')) {
                    var $reviewsContainer = $this.closest('.reviews-container').find('.reviews-list-container');
                    
                    if ($reviewsContainer.hasClass('expanded')) {
                        $reviewsContainer.removeClass('expanded');
                        $this.text('Show More');
                    } else {
                        $reviewsContainer.addClass('expanded');
                        $this.text('Show Less');
                    }
                }
                // Check if this is a notes toggle (for the entire notes section)
                else if ($this.hasClass('notes-toggle')) {
                    var $notesContainer = $this.closest('.notes-container').find('.notes-list-container');
                    
                    if ($notesContainer.hasClass('expanded')) {
                        $notesContainer.removeClass('expanded');
                        $this.text('Show More');
                    } else {
                        $notesContainer.addClass('expanded');
                        $this.text('Show Less');
                    }
                }
                // Check if this is a note content toggle (for individual note content)
                else if ($this.hasClass('note-content-toggle')) {
                    var $hiddenContent = $this.closest('.note-content').find('.note-hidden-content');

                    if ($hiddenContent.is(':visible')) {
                        $hiddenContent.hide();
                        $this.text('Show more');
                    } else {
                        $hiddenContent.show();
                        $this.text('Show less');
                    }
                }
                // Handle legacy notices show more (existing functionality - fallback)
                else {
                    var $hiddenContent = $this.closest('.note-content').find('.note-hidden-content');

                    if ($hiddenContent.length > 0) {
                        if ($hiddenContent.is(':visible')) {
                            $hiddenContent.hide();
                            $this.text('Show more');
                        } else {
                            $hiddenContent.show();
                            $this.text('Show less');
                        }
                    }
                }
            });

            // Handle tip buttons
            $('.tip-button').on('click', function() {
                var $this = $(this);
                var reviewId = $this.data('review-id');
                var rewardOption = $this.data('reward-option');
                var $feeNotice = $this.closest('.reward-options').find('.fee-notice');
                var $rewardOptions = $this.closest('.reward-options');

                // Disable all buttons in this card to prevent multiple clicks
                var $allButtons = $this.closest('.reward-buttons').find('.tip-button, .no-need-button');
                $allButtons.prop('disabled', true).addClass('disabled');

                // Hide the fee notice
                $feeNotice.hide();

                // Send AJAX request to tip the notice
                $.ajax({
                    url: '{{ route("tip.notice") }}',
                    type: 'POST',
                    data: {
                        review_id: reviewId,
                        reward_option: rewardOption,
                        _token: '{{ csrf_token() }}'
                    },
                    success: function(response) {
                        if (response.success) {
                            // Replace buttons with success message
                            $this.closest('.reward-buttons').html(
                                '<div class="text-success"><i class="ri-checkbox-circle-line"></i> ' +
                                'You\'ve tipped this notice with ' + response.reward_amount + ' Rewards.</div>'
                            );

                            // Permanently remove the fee notice
                            $feeNotice.remove();
                        } else {
                            let errorMessage = 'An error occurred while tipping the notice';
                            if (response.message) {
                                errorMessage = response.message;
                            }

                            // Show inline error message instead of alert
                            $this.closest('.reward-buttons').append(
                                '<div class="text-danger mt-2"><i class="ri-error-warning-line"></i> ' +
                                errorMessage + '</div>'
                            );

                            // Re-enable buttons in case of error
                            $allButtons.prop('disabled', false).removeClass('disabled');
                            // Show the fee notice again in case of error
                            $feeNotice.show();
                        }
                    },
                    error: function(xhr) {
                        // Re-enable buttons in case of error
                        $allButtons.prop('disabled', false).removeClass('disabled');

                        let errorMessage = 'An error occurred while tipping the notice';
                        if (xhr.responseJSON && xhr.responseJSON.message) {
                            errorMessage = xhr.responseJSON.message;
                        }

                        // Show inline error message instead of alert
                        $this.closest('.reward-buttons').append(
                            '<div class="text-danger mt-2"><i class="ri-error-warning-line"></i> ' +
                            errorMessage + '</div>'
                        );

                        // Show the fee notice again in case of error
                        $feeNotice.show();
                    }
                });
            });

            // Handle "No need" button
            $('.no-need-button').on('click', function() {
                var $this = $(this);
                var reviewId = $this.data('review-id');
                var $feeNotice = $this.closest('.reward-options').find('.fee-notice');
                var $rewardOptions = $this.closest('.reward-options');

                // Disable all buttons in this card to prevent multiple clicks
                var $allButtons = $this.closest('.reward-buttons').find('.tip-button, .no-need-button');
                $allButtons.prop('disabled', true).addClass('disabled');

                // Hide the fee notice
                $feeNotice.hide();

                // Send AJAX request to mark as "No need"
                $.ajax({
                    url: '{{ route("tip.notice") }}',
                    type: 'POST',
                    data: {
                        review_id: reviewId,
                        reward_option: 'no_need',
                        _token: '{{ csrf_token() }}'
                    },
                    success: function(response) {
                        if (response.success) {
                            // Replace buttons with success message
                            $this.closest('.reward-buttons').html(
                                '<div class="text-primary"><i class="ri-information-line"></i> ' +
                                'You\'ve decided not to tip this Notice with Rewards.</div>'
                            );

                            // Permanently remove the fee notice
                            $feeNotice.remove();
                        } else {
                            let errorMessage = 'An error occurred while processing your request';
                            if (response.message) {
                                errorMessage = response.message;
                            }

                            // Show inline error message instead of alert
                            $this.closest('.reward-buttons').append(
                                '<div class="text-danger mt-2"><i class="ri-error-warning-line"></i> ' +
                                errorMessage + '</div>'
                            );

                            // Re-enable buttons in case of error
                            $allButtons.prop('disabled', false).removeClass('disabled');
                            // Show the fee notice again in case of error
                            $feeNotice.show();
                        }
                    },
                    error: function(xhr) {
                        // Re-enable buttons in case of error
                        $allButtons.prop('disabled', false).removeClass('disabled');

                        let errorMessage = 'An error occurred while processing your request';
                        if (xhr.responseJSON && xhr.responseJSON.message) {
                            errorMessage = xhr.responseJSON.message;
                        }

                        // Show inline error message instead of alert
                        $this.closest('.reward-buttons').append(
                            '<div class="text-danger mt-2"><i class="ri-error-warning-line"></i> ' +
                            errorMessage + '</div>'
                        );

                        // Show the fee notice again in case of error
                        $feeNotice.show();
                    }
                });
            });
        });
    </script>
@endsection


