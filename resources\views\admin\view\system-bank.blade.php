@extends('master-layout.master-layout')
<link rel="stylesheet" href="{{ asset('admin/css/style.css') }}">
@section('page_title', 'System Bank')
@section('page_content')
    <div class="container">
        <section class="padding-medium text-center">
            <h2>System Bank</h2>
        </section>
    </div>

    <div class="dashboard">
        <div class="container-fluid p-5">
            <div class="row">
                <div class="col-md-3">
                    @include('admin.includes.navigation')
                </div>
                <div class="col-md-9">
                    <div class="content-panel">
                        <!-- Current Balance Card -->
                        <div class="row">
                            <div class="col-lg-6 col-md-12 mb-20">
                                <div class="card-box pd-20 mb-20">
                                    <div class="clearfix mb-20">
                                        <div class="pull-left">
                                            <h4 class="text-blue h4">Current System Bank Balance</h4>
                                        </div>
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold
                                        {{ $systemBankBalance->value < 0 ? 'text-danger' : 'text-success' }}">
                                        {{ \App\Helpers\NumberHelper::formatBalance($systemBankBalance->value) }} Rewards
                                    </div>
                                    <p class="text-muted font-14 mt-2">
                                        The System Bank is replenished by system cuts when books are boosted.
                                        It is used to fund rewards for author notices.
                                    </p>
                                </div>
                            </div>
                        </div>

                        <!-- System Bank Settings Form -->
                        <div class="bg-secondary-subtle p-5 rounded-4 mb-5">
                            <div class="clearfix mb-20">
                                <div class="pull-left">
                                    <h4 class="text-blue h4">System Bank Settings</h4>
                                    <p class="mb-30">Configure the System Bank reward options and system cut</p>
                                </div>
                            </div>

                            <form action="{{ route('admin.systemBank.update') }}" method="post">
                                @csrf
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group row">
                                            <label class="col-sm-12 col-md-4 col-form-label">Reward Option 1 (R1)</label>
                                            <div class="col-sm-12 col-md-8">
                                                <input type="number" min="1" name="notice_reward_1" value="{{ $noticeReward1->value }}" class="form-control" required>
                                            </div>
                                        </div>

                                        <div class="form-group row">
                                            <label class="col-sm-12 col-md-4 col-form-label">Reward Option 2 (R2)</label>
                                            <div class="col-sm-12 col-md-8">
                                                <input type="number" min="1" name="notice_reward_2" value="{{ $noticeReward2->value }}" class="form-control" required>
                                            </div>
                                        </div>

                                        <div class="form-group row">
                                            <label class="col-sm-12 col-md-4 col-form-label">Reward Option 3 (R3)</label>
                                            <div class="col-sm-12 col-md-8">
                                                <input type="number" min="1" name="notice_reward_3" value="{{ $noticeReward3->value }}" class="form-control" required>
                                            </div>
                                        </div>

                                        <div class="form-group row">
                                            <label class="col-sm-12 col-md-4 col-form-label">Notice Fee (R0)</label>
                                            <div class="col-sm-12 col-md-8">
                                                <input type="number" min="0" name="notice_fee" value="{{ isset($noticeFee) ? $noticeFee->value : 2 }}" class="form-control" required>
                                                <small class="form-text text-muted">
                                                    Fee charged to author when tipping a notice
                                                </small>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="form-group row">
                                            <label class="col-sm-12 col-md-4 col-form-label">System Cut (%)</label>
                                            <div class="col-sm-12 col-md-8">
                                                <input type="number" min="0" name="system_cut" value="{{ $systemCut->value }}" class="form-control" required>
                                                <small class="form-text text-muted">
                                                    Percentage of book boost points that go to the System Bank
                                                </small>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group row">
                                    <div class="col-md-12 text-right">
                                        <button type="submit" class="btn btn-primary">Update Settings</button>
                                    </div>
                                </div>
                            </form>
                        </div>

                        <!-- Transaction History -->
                        <div class="bg-secondary-subtle p-5 rounded-4 mb-5">
                            <div class="clearfix mb-20">
                                <div class="pull-left">
                                    <h4 class="text-blue h4">System Bank Transaction History</h4>
                                </div>
                            </div>

                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Date</th>
                                            <th>Type</th>
                                            <th>Amount</th>
                                            <th>Description</th>
                                            <th>Book</th>
                                            <th>User</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @if(count($transactions) > 0)
                                            @foreach($transactions as $transaction)
                                                <tr>
                                                    <td>{{ $transaction->created_at->format('M d, Y H:i') }}</td>
                                                    <td>
                                                        @if($transaction->transaction_type == 'in')
                                                            <span class="badge badge-success">System Cut (In)</span>
                                                        @else
                                                            <span class="badge badge-warning">Notice Reward (Out)</span>
                                                        @endif
                                                    </td>
                                                    <td>
                                                        @if($transaction->transaction_type == 'in')
                                                            <span class="text-success">+{{ $transaction->amount }}</span>
                                                        @else
                                                            <span class="text-danger">-{{ $transaction->amount }}</span>
                                                        @endif
                                                    </td>
                                                    <td>{{ $transaction->description }}</td>
                                                    <td>
                                                        @if($transaction->book)
                                                            {{ Str::limit($transaction->book->title, 30) }}
                                                        @else
                                                            N/A
                                                        @endif
                                                    </td>
                                                    <td>
                                                        @if($transaction->user)
                                                            {{ $transaction->user->fullName }}
                                                        @else
                                                            N/A
                                                        @endif
                                                    </td>
                                                </tr>
                                            @endforeach
                                        @else
                                            <tr>
                                                <td colspan="6" class="text-center">No transactions found.</td>
                                            </tr>
                                        @endif
                                    </tbody>
                                </table>
                            </div>

                            <!-- Pagination -->
                            <div class="mt-3">
                                {{ $transactions->links() }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection