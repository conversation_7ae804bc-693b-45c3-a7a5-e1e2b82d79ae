<!DOCTYPE html>
<html>

<head>
    <title>Auteurs.space - <?php echo $__env->yieldContent('page_title'); ?></title>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="format-detection" content="telephone=no">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="author" content="">
    <meta name="keywords" content="">
    <meta name="description" content="">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">


    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css">

    <!-- Icon fonts -->
    <link href="https://cdn.jsdelivr.net/npm/remixicon@2.2.0/fonts/remixicon.css" rel="stylesheet">

    <!-- Swiper slider -->
    <link rel="stylesheet" href="<?php echo e(asset('css/swiper.min.css')); ?>">
    <script src="<?php echo e(asset('js/swiper-bundle.min.js')); ?>"></script>
    <!-- Home page specific styles -->
    <link rel="stylesheet" href="<?php echo e(asset('css/home.css')); ?>">

    <!-- Master CSS file for consistent styling across the application -->
    <link rel="stylesheet" href="<?php echo e(asset('css/auteurs-master.css')); ?>">

    <!-- Flash Messages and Quiz Results Styling -->
    <link rel="stylesheet" href="<?php echo e(asset('css/flash-messages.css')); ?>">

    <!-- Alert Overrides - Must be loaded after all other CSS -->
    <link rel="stylesheet" href="<?php echo e(asset('css/alert-overrides.css')); ?>">

    <!-- Notification Styles - Custom styling for notifications dropdown -->
    <link rel="stylesheet" href="<?php echo e(asset('css/notification-styles.css')); ?>">

    <!-- Logo Styles - Consolidated logo styling -->
    <link rel="stylesheet" href="<?php echo e(asset('css/logo-styles.css')); ?>">

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link
        href="https://fonts.googleapis.com/css2?family=Lato:ital,wght@0,100;0,300;0,400;0,700;0,900;1,100;1,300;1,400;1,700;1,900&family=Montserrat:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap"
        rel="stylesheet">

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Modernizr -->
    <script src="<?php echo e(asset('js/modernizr.js')); ?>"></script>

    <!-- Dynamic Color Variables - These override the static CSS variables in the master CSS -->
    <style>
        /* Dynamic color variables generated by ColorHelper */
        <?php echo App\Helpers\ColorHelper::getColorCssVariables(); ?>


        /* Logo styles moved to dedicated CSS file: /css/logo-styles.css */
    </style>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const logoH1Elements = document.querySelectorAll('.logo h1');
            logoH1Elements.forEach(function(element) {
                if (element.textContent.trim() === 'Book Preview') {
                    element.classList.add('book-preview-title');
                }
            });
        });
    </script>

    <!-- Header Starts -->
    <div class="mobmenu p-2">
        <?php if(auth()->guard()->check()): ?>
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                const notificationIcon = document.getElementById('showMobileNotification');
                const notificationContainer = document.getElementById('mobileNotificationContainer');
                const notificationBadge = document.querySelector('.mobile-user-info .notification-badge');

                if (notificationIcon) {
                    notificationIcon.addEventListener('click', function() {
                        // Make AJAX call to mark notifications as read
                        fetch('<?php echo e(route('notifications.markAllAsReadAjax')); ?>', {
                            method: 'POST',
                            headers: {
                                'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>',
                                'Content-Type': 'application/json',
                                'Accept': 'application/json'
                            }
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                // If successful, remove the unread badge
                                if (notificationBadge) {
                                    notificationBadge.style.display = 'none';
                                }
                                // Optionally, update the appearance of notifications in the dropdown
                                // to indicate they are now read (e.g., change background color)
                                const unreadNotifications = notificationContainer.querySelectorAll('li');
                                unreadNotifications.forEach(li => {
                                    // Assuming you have a class to indicate unread status
                                    // li.classList.remove('unread');
                                });
                            }
                        })
                        .catch(error => {
                            console.error('Error marking notifications as read:', error);
                        });
                    });
                }

                const desktopNotificationIcon = document.getElementById('showNotification');
                const desktopNotificationContainer = document.getElementById('notificationContainer');
                const desktopNotificationBadge = document.querySelector('.deskHEader .notification-badge');

                if (desktopNotificationIcon) {
                    desktopNotificationIcon.addEventListener('click', function() {
                        // Make AJAX call to mark notifications as read
                        fetch('<?php echo e(route('notifications.markAllAsReadAjax')); ?>', {
                            method: 'POST',
                            headers: {
                                'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>',
                                'Content-Type': 'application/json',
                                'Accept': 'application/json'
                            }
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                // If successful, remove the unread badge
                                if (desktopNotificationBadge) {
                                    desktopNotificationBadge.style.display = 'none';
                                }
                                // Optionally, update the appearance of notifications in the dropdown
                                // to indicate they are now read
                                const unreadNotifications = desktopNotificationContainer.querySelectorAll('li');
                                unreadNotifications.forEach(li => {
                                    // Assuming you have a class to indicate unread status
                                    // li.classList.remove('unread');
                                });
                            }
                        })
                        .catch(error => {
                            console.error('Error marking notifications as read:', error);
                        });
                    });
                }
            });
        </script>
        <?php endif; ?>
        <div class="logo text-center">
            <a href="<?php echo e(route('home')); ?>"><h1><?php echo e(\App\Helpers\PageTitleHelper::getPageTitle()); ?></h1></a>
        </div>
        <div class="menu-toggler">
            <i class="ri-menu-3-line"></i>
        </div>
        <ul id="menu">
            <div class="menu-toggler">
                <i class="ri-close-line"></i>
            </div>
            <?php if(auth()->guard()->guest()): ?>

                <li>
                    <a href="<?php echo e(route('home')); ?>#how">How's</a>
                </li>
                <li>
                    <a href="<?php echo e(route('home')); ?>#faqs">FAQs</a>
                </li>
                <li>
                    <a href="<?php echo e(route('home')); ?>#plans">Plans</a>
                </li>
                <li>
                    <?php if(!request()->routeIs('login')): ?>
                    <a href="<?php echo e(route('login')); ?>" class="btn btn-primary">Login</a>
                    <?php endif; ?>
                    <?php
                        $registerRoute = \App\Services\SubscriptionFlowService::getCurrentFlow() === 'production'
                            ? route('subscription.public-plans')
                            : route('register') . '?plan=auteur';
                    ?>
                    <a href="<?php echo e($registerRoute); ?>" class="btn btn-primary">Register</a>
                </li>
                <!-- Subscription link removed as it's now integrated into the profile page -->

            <?php else: ?>
                <li class="position-relative header-user-name">
                    <a href="#">Hi <?php echo e(Auth::user()->fullName); ?></a>
                </li>
                <li class="mobile-user-info">
                    <div class="mobile-notification-balance">
                        <span class='header-notification'>
                            <span id='showMobileNotification' class="header-notification-icon">&#128276;</span>
                            <?php
                            $userID = Auth::user()->id;
                            $notificationArray = [];
                            $unreadCount = 0;

                            // Get books published by this user
                            $books = \App\Models\Book::where('publish_by', $userID)->get();
                            $booksID = $books->pluck('id')->toArray();

                            // Get all author notifications where either:
                            // 1. The notification is for one of their books, OR
                            // 2. The notification is directly addressed to them
                            $authorNotifications = \App\Models\Activity::where('notification_type', 'author')
                                ->where(function($query) use ($userID, $booksID) {
                                    $query->whereIn('bookId', $booksID)
                                          ->orWhere('userid', $userID);
                                })
                                ->orderBy('created_at', 'desc')
                                ->get();

                            // Double-check for any missed author notifications
                            $missedAuthorNotifications = \App\Models\Activity::where('notification_type', 'author')
                                ->whereIn('bookId', $booksID)
                                ->whereNull('userid')
                                ->orderBy('created_at', 'desc')
                                ->get();

                            // Merge any missed notifications
                            if ($missedAuthorNotifications->count() > 0) {
                                $authorNotifications = $authorNotifications->merge($missedAuthorNotifications);
                            }

                            // Get reader notifications for this user (both 'reader' and 'reviewer' types)
                            $readerNotifications = \App\Models\Activity::where('userid', $userID)
                                ->where(function($query) {
                                    $query->where('notification_type', 'reader')
                                          ->orWhere('notification_type', 'reviewer');
                                })
                                ->orderBy('created_at', 'desc')
                                ->get();

                            // Process author notifications
                            foreach ($authorNotifications as $notification) {
                                $notificationArray[] = [
                                    'id' => $notification->id,
                                    'reason' => $notification->reason,
                                    'created_at' => $notification->created_at->format('Y-m-d H:i:s'),
                                    'read' => $notification->read ?? false
                                ];
                                if (!($notification->read ?? false)) {
                                    $unreadCount++;
                                }
                            }

                            // Process reader notifications
                            foreach ($readerNotifications as $notification) {
                                $notificationArray[] = [
                                    'id' => $notification->id,
                                    'reason' => $notification->reason,
                                    'created_at' => $notification->created_at->format('Y-m-d H:i:s'),
                                    'read' => $notification->read ?? false
                                ];
                                if (!($notification->read ?? false)) {
                                    $unreadCount++;
                                }
                            }

                            // Sort notifications by date
                            usort($notificationArray, function ($a, $b) {
                                return strtotime($b['created_at']) - strtotime($a['created_at']);
                            });

                            $noActivities = empty($notificationArray);

                            // Get user wallet balance
                            $user = Auth::user();
                            $wallet = $user->wallet;
                            ?>

                            <?php if($unreadCount > 0): ?>
                                <span class="notification-badge"><?php echo e($unreadCount); ?></span>
                            <?php endif; ?>
                            <div id='mobileNotificationContainer' class='notification-container'>
                                <ul>
                                    <?php if($noActivities): ?>
                                        <li class="no-activity">No Activity Yet</li>
                                    <?php else: ?>
                                        <?php $__currentLoopData = $notificationArray; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $notification): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <li>
                                                <div class="notification-content">
                                                    <div class="notification-message"><?php echo e($notification['reason']); ?></div>
                                                    <div class="notification-date"><?php echo e(\Carbon\Carbon::parse($notification['created_at'])->format('M d, H:i')); ?></div>
                                                </div>
                                            </li>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    <?php endif; ?>
                                </ul>
                            </div>
                        </span>
                        <p class="header-wallet-balance"><i class="ri-heart-fill"></i> <?php echo e(\App\Helpers\NumberHelper::formatBalance($wallet->currentBalance)); ?></p>
                    </div>
                </li>
                <li class="nav-radio-group">
                    <label>
                        <input type="radio" name="navigation" value="author" <?php echo e(request()->routeIs('author') ? 'checked' : ''); ?> onchange="window.location.href='<?php echo e(route('author')); ?>'">
                        <span>Auteur</span>
                    </label>
                    <label>
                        <input type="radio" name="navigation" value="reader" <?php echo e(request()->routeIs('reader') ? 'checked' : ''); ?> onchange="window.location.href='<?php echo e(route('reader')); ?>'">
                        <span>Reader</span>
                    </label>
                    <label>
                        <input type="radio" name="navigation" value="vault" <?php echo e(request()->routeIs('vault') ? 'checked' : ''); ?> onchange="window.location.href='<?php echo e(route('vault')); ?>'">
                        <span>Vault</span>
                    </label>
                </li>
                <?php if(Auth::user()->is_admin): ?>
                <li>
                    <a href="<?php echo e(route('allBooksDashboard')); ?>">Admin Dashboard</a>
                </li>
                <?php endif; ?>
                <li>
                    <a href="<?php echo e(route('profile.index')); ?>">Profile</a>
                </li>
                <!-- Subscription link removed as it's now integrated into the profile page -->
                <li>
                    <a class="btn btn-primary" href="<?php echo e(route('Log Out')); ?>">Logout</a>
                </li>
            <?php endif; ?>
        </ul>
    </div>
    <header class="deskHEader">
        <div class="container">
            <div class="row justify-content-between align-items-center">
                <div class="col-md-3">
                    <div class="nav-container">
                        <ul>
                            <?php if(auth()->guard()->guest()): ?>
                                <li>
                                    <a href="<?php echo e(route('home')); ?>#how">How's</a>
                                </li>
                                <li>
                                    <a href="<?php echo e(route('home')); ?>#faqs">FAQs</a>
                                </li>
                                <li>
                                    <a href="<?php echo e(route('home')); ?>#plans">Plans</a>
                                </li>
                            <?php else: ?>
                                <li class="nav-radio-group">
                                    <label>
                                        <input type="radio" name="navigation-desk" value="author" <?php echo e(request()->routeIs('author') ? 'checked' : ''); ?> onchange="window.location.href='<?php echo e(route('author')); ?>'">
                                        <span>Auteur</span>
                                    </label>
                                    <label>
                                        <input type="radio" name="navigation-desk" value="reader" <?php echo e(request()->routeIs('reader') ? 'checked' : ''); ?> onchange="window.location.href='<?php echo e(route('reader')); ?>'">
                                        <span>Reader</span>
                                    </label>
                                    <label>
                                        <input type="radio" name="navigation-desk" value="vault" <?php echo e(request()->routeIs('vault') ? 'checked' : ''); ?> onchange="window.location.href='<?php echo e(route('vault')); ?>'">
                                        <span>Vault</span>
                                    </label>
                                </li>
                            <?php endif; ?>
                        </ul>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="logo text-center">
                        <a href="<?php echo e(route('home')); ?>"><h1><?php echo e(\App\Helpers\PageTitleHelper::getPageTitle()); ?></h1></a>
                    </div>
                </div>
                <div class="col-md-3">


                    <div class="nav-container" style="justify-content: flex-end;">
                        <ul>
                            <?php if(auth()->guard()->guest()): ?>
                                <?php if(!request()->routeIs('login')): ?>
                                <li>
                                    <a href="<?php echo e(route('login')); ?>">Login</a>
                                </li>
                                <?php endif; ?>
                                <li>
                                    <?php
                                        $registerRoute = \App\Services\SubscriptionFlowService::getCurrentFlow() === 'production'
                                            ? route('subscription.public-plans')
                                            : route('register') . '?plan=auteur';
                                    ?>
                                    <a href="<?php echo e($registerRoute); ?>" class="btn btn-primary">Register</a>
                                </li>
                            <?php else: ?>
                                <li class="position-relative header-user-name" id="userTrigger">
                                    <a href="#">Hi <?php echo e(Auth::user()->fullName); ?></a>
                                    <div id="userDropdown">
                                        <ul>
                                            <li>
                                                <a href="/author-dashboard">Dashboard</a>
                                            </li>
                                            <?php if(Auth::user()->is_admin): ?>
                                            <li>
                                                <a href="<?php echo e(route('allBooksDashboard')); ?>">Admin Dashboard</a>
                                            </li>
                                            <?php endif; ?>
                                            <li>
                                                <a href="<?php echo e(route('profile.index')); ?>">Profile</a>
                                            </li>
                                            <!-- Subscription link removed as it's now integrated into the profile page -->
                                            <li>
                                                <a href="<?php echo e(route('Log Out')); ?>">Logout</a>
                                            </li>
                                        </ul>
                                    </div>
                                </li>
                                <li>
                                    <span class='header-notification'>
                                        <span id='showNotification' class="header-notification-icon">&#128276;</span>
                                        <?php
                                        $userID = Auth::user()->id;
                                        $notificationArray = [];
                                        $unreadCount = 0;

                                        // Get books published by this user
                                        $books = \App\Models\Book::where('publish_by', $userID)->get();
                                        $booksID = $books->pluck('id')->toArray();

                                        // Get all author notifications where either:
                                        // 1. The notification is for one of their books, OR
                                        // 2. The notification is directly addressed to them
                                        $authorNotifications = \App\Models\Activity::where('notification_type', 'author')
                                            ->where(function($query) use ($userID, $booksID) {
                                                $query->whereIn('bookId', $booksID)
                                                      ->orWhere('userid', $userID);
                                            })
                                            ->orderBy('created_at', 'desc')
                                            ->get();

                                        // Log the query parameters for debugging
                                        \Illuminate\Support\Facades\Log::info("Author notification query parameters", [
                                            'userID' => $userID,
                                            'booksID' => $booksID,
                                            'sql' => \App\Models\Activity::where('notification_type', 'author')
                                                ->where(function($query) use ($userID, $booksID) {
                                                    $query->whereIn('bookId', $booksID)
                                                          ->orWhere('userid', $userID);
                                                })->toSql()
                                        ]);

                                        // Double-check for any missed author notifications
                                        // This ensures we catch notifications for books published by this user
                                        // even if they weren't properly linked in the notification
                                        $missedAuthorNotifications = \App\Models\Activity::where('notification_type', 'author')
                                            ->whereIn('bookId', $booksID)
                                            ->whereNull('userid')
                                            ->orderBy('created_at', 'desc')
                                            ->get();

                                        // Merge any missed notifications
                                        if ($missedAuthorNotifications->count() > 0) {
                                            \Illuminate\Support\Facades\Log::info("Found {$missedAuthorNotifications->count()} missed author notifications");
                                            $authorNotifications = $authorNotifications->merge($missedAuthorNotifications);
                                        }

                                        // Get reader notifications for this user (both 'reader' and 'reviewer' types)
                                        $readerNotifications = \App\Models\Activity::where('userid', $userID)
                                            ->where(function($query) {
                                                $query->where('notification_type', 'reader')
                                                      ->orWhere('notification_type', 'reviewer');
                                            })
                                            ->orderBy('created_at', 'desc')
                                            ->get();

                                        // Debug log the query with more details
                                        \Illuminate\Support\Facades\Log::info("Fetched {$authorNotifications->count()} author notifications and {$readerNotifications->count()} reader notifications for user #{$userID}");

                                        // Log details of each author notification for debugging
                                        foreach ($authorNotifications as $notification) {
                                            \Illuminate\Support\Facades\Log::info("Author notification details", [
                                                'id' => $notification->id,
                                                'userid' => $notification->userid,
                                                'bookId' => $notification->bookId,
                                                'notification_type' => $notification->notification_type,
                                                'reason' => $notification->reason,
                                                'created_at' => $notification->created_at
                                            ]);
                                        }

                                        // Process author notifications
                                        foreach ($authorNotifications as $notification) {
                                            $notificationArray[] = [
                                                'id' => $notification->id,
                                                'reason' => $notification->reason,
                                                'created_at' => $notification->created_at->format('Y-m-d H:i:s'),
                                                'read' => $notification->read ?? false
                                            ];
                                            if (!($notification->read ?? false)) {
                                                $unreadCount++;
                                            }
                                        }

                                        // Process reader notifications
                                        foreach ($readerNotifications as $notification) {
                                            $notificationArray[] = [
                                                'id' => $notification->id,
                                                'reason' => $notification->reason,
                                                'created_at' => $notification->created_at->format('Y-m-d H:i:s'),
                                                'read' => $notification->read ?? false
                                            ];
                                            if (!($notification->read ?? false)) {
                                                $unreadCount++;
                                            }
                                        }

                                        // Sort notifications by date
                                        usort($notificationArray, function ($a, $b) {
                                            return strtotime($b['created_at']) - strtotime($a['created_at']);
                                        });

                                        $noActivities = empty($notificationArray);
                                        ?>

                                        <?php if($unreadCount > 0): ?>
                                            <span class="notification-badge"><?php echo e($unreadCount); ?></span>
                                        <?php endif; ?>
                                        <div id='notificationContainer' class='notification-container'>
                                            <ul>
                                                <?php if($noActivities): ?>
                                                    <li class="no-activity">No Activity Yet</li>
                                                <?php else: ?>
                                                    <?php $__currentLoopData = $notificationArray; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $notification): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                        <li>
                                                            <div class="notification-content">
                                                                <div class="notification-message"><?php echo e($notification['reason']); ?></div>
                                                                <div class="notification-date"><?php echo e(\Carbon\Carbon::parse($notification['created_at'])->format('M d, H:i')); ?></div>
                                                            </div>
                                                        </li>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                <?php endif; ?>
                                            </ul>
                                        </div>
                                    </span>
                                </li>
                                <li>
                                    <?php
                                        $user = Auth::user();
                                        $wallet = $user->wallet; // Assuming you have set up the User-Wallet relationship
                                    ?>


                                    <p class="header-wallet-balance"><i class="ri-heart-fill"></i> <?php echo e(\App\Helpers\NumberHelper::formatBalance($wallet->currentBalance)); ?></p>
                                </li>
                            <?php endif; ?>
                        </ul>
                    </div>

                </div>
            </div>
        </div>

        <div class="container">


        </div>
    </header>
    <!-- Header Ends -->
<?php /**PATH I:\_auteurs.space\auteurs.space.set.clean\resources\views/includes/header.blade.php ENDPATH**/ ?>